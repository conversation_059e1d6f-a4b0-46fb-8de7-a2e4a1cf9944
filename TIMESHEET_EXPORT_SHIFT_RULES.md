# Thêm Cột AA, AB, AC - Thông Tin Ca Làm Việc từ Shift Rule Histories

## Tổng quan
Đã thêm 3 cột mới AA, AB, AC vào export timesheets để hiển thị thông tin ca làm việc từ `shift_rule_histories` nếu có, nếu không thì fallback về `shifts`.

## Các cột mới đã thêm

### Cột AA (Z): Gi<PERSON> bắt đầu
- **Nguồn**: `shift_rule_histories.start_time` hoặc `shifts.start_time`
- **Định dạng**: HH:mm (ví dụ: 08:30)
- **Logic**: Ưu tiên từ shift_rule_histories, fallback về shifts

### Cột AB (AA): G<PERSON><PERSON> kết thúc  
- **Nguồn**: `shift_rule_histories.end_time` hoặc `shifts.end_time`
- **Định dạng**: HH:mm (ví dụ: 17:30)
- **Logic**: Ưu tiên từ shift_rule_histories, fallback về shifts

### Cột AC (AB): Giờ nghỉ
- **Nguồn**: `shift_rule_histories.break_times` hoặc "Không có dữ liệu"
- **Định dạng**: HH:mm-HH:mm, HH:mm-HH:mm (ví dụ: 12:00-13:30, 15:30-15:45)
- **Logic**: Chỉ có từ shift_rule_histories (JSON format)

## Logic ưu tiên dữ liệu

### 1. Có shift_rule_histories
```
Timesheet -> shift_rule_history_id -> shift_rule_histories
```
- **AA**: shift_rule_histories.start_time
- **AB**: shift_rule_histories.end_time  
- **AC**: shift_rule_histories.break_times (parsed từ JSON)

### 2. Không có shift_rule_histories (fallback)
```
Timesheet -> shift_id -> shifts
```
- **AA**: shifts.start_time
- **AB**: shifts.end_time
- **AC**: "Không có dữ liệu"

## Cấu trúc dữ liệu

### shift_rule_histories.break_times (JSON)
```json
[
  {
    "start": "12:00",
    "end": "13:30"
  },
  {
    "start": "15:30", 
    "end": "15:45"
  }
]
```

### Hiển thị trong Excel
```
12:00-13:30, 15:30-15:45
```

## Files đã thay đổi

### 1. TimesheetRepository.php
**File**: `app/Repositories/Eloquent/TimesheetRepository.php`
**Method**: `queryToExport()`

**Thay đổi:**
- Thêm LEFT JOIN với `shift_rule_histories`
- Sử dụng COALESCE để ưu tiên dữ liệu từ shift_rule_histories
- Thêm các cột: `shift_break_times`, `shift_other_rules`, `rule_effective_from`, `rule_effective_to`

```sql
-- Ưu tiên lấy từ shift_rule_histories, fallback về shifts
COALESCE(MIN(shift_rule_histories.start_time), MIN(shifts.start_time)) as shift_start_time
COALESCE(MAX(shift_rule_histories.end_time), MAX(shifts.end_time)) as shift_end_time
COALESCE(MIN(shift_rule_histories.workday_min_1), MIN(shifts.workday_min_1)) as shift_workday_min_1
COALESCE(MIN(shift_rule_histories.workday_min_2), MIN(shifts.workday_min_2)) as shift_workday_min_2

-- Thêm thông tin từ shift_rule_histories
MIN(shift_rule_histories.break_times) as shift_break_times
MIN(shift_rule_histories.other_rule_json) as shift_other_rules
MIN(shift_rule_histories.effective_from) as rule_effective_from
MIN(shift_rule_histories.effective_to) as rule_effective_to
```

### 2. TimesheetHeadings.php
**File**: `app/Exports/Timesheet/TimesheetSheet/TimesheetHeadings.php`

**Thay đổi:**
- Thêm header row 1: "THÔNG TIN CA" (merge Z1:AB1)
- Thêm header row 2: "Giờ bắt đầu", "Giờ kết thúc", "Giờ nghỉ"
- Thêm width cho cột 26, 27, 28
- Thêm styling cho nhóm cột mới

### 3. TimesheetRows.php
**File**: `app/Exports/Timesheet/TimesheetSheet/TimesheetRows.php`

**Thay đổi chính:**
- Thêm 3 cột mới vào method `map()`
- Thêm fields mới vào `calculateAttendanceData()`
- Thêm method `populateShiftRuleData()`
- Thêm method `formatTime()` và `formatBreakTimes()`
- Cập nhật `columnFormats()` cho cột mới

**Methods mới:**
```php
private function populateShiftRuleData($data, $timesheet)
private function formatTime($time)
private function formatBreakTimes($breakTimesJson)
```

## Test case

### Dữ liệu test (User ID 2845, ngày 2025-06-02):
- **Shift**: CA HC (08:30:00 - 17:30:00)
- **Shift Rule History**: Có dữ liệu (effective_from: 2025-05-06)
- **Break Times**: `[{"start":"12:00","end":"13:30"}]`

### Kết quả export:
- **AA (Giờ bắt đầu)**: 08:30
- **AB (Giờ kết thúc)**: 17:30  
- **AC (Giờ nghỉ)**: 12:00-13:30

## Định dạng Excel

### Headers
- **Row 1**: Merge cells Z1:AB1 với text "THÔNG TIN CA"
- **Row 2**: "Giờ bắt đầu", "Giờ kết thúc", "Giờ nghỉ"

### Styling
- **Background**: Light blue (#D9E2F3) cho header group
- **Width**: 1.00 inch cho AA, AB; 1.50 inch cho AC
- **Format**: Text format (@) cho tất cả 3 cột

### Data format
- **Time**: HH:mm (08:30, 17:30)
- **Break times**: HH:mm-HH:mm, HH:mm-HH:mm
- **No data**: "Không có dữ liệu"

## Lợi ích

### 1. Truy xuất lịch sử
- Hiển thị quy tắc ca làm việc tại thời điểm chấm công
- Không bị ảnh hưởng khi thay đổi ca làm việc sau này

### 2. Thông tin chi tiết
- Giờ nghỉ cụ thể từ shift_rule_histories
- Có thể có nhiều khoảng nghỉ trong ngày

### 3. Tương thích ngược
- Fallback về shifts nếu không có shift_rule_histories
- Không ảnh hưởng dữ liệu cũ

## Lưu ý

### 1. Performance
- Query đã được optimize với LEFT JOIN
- Sử dụng COALESCE để tránh NULL values
- GROUP BY vẫn hoạt động bình thường

### 2. Dữ liệu
- shift_rule_histories có thể NULL nếu timesheet cũ
- break_times là JSON, cần parse để hiển thị
- Thời gian luôn được format về HH:mm

### 3. Mở rộng
- Có thể thêm other_rule_json vào cột khác
- Có thể hiển thị effective_from/effective_to
- Có thể thêm validation cho break_times format

## Kết luận

✅ **Đã hoàn thành:** Thêm 3 cột AA, AB, AC hiển thị thông tin ca làm việc từ shift_rule_histories với fallback về shifts

🎯 **Kết quả:** Export timesheets giờ có thông tin chi tiết về ca làm việc theo lịch sử, bao gồm giờ nghỉ cụ thể

🔧 **Logic:** Ưu tiên shift_rule_histories, fallback về shifts, format dữ liệu thân thiện với người dùng
