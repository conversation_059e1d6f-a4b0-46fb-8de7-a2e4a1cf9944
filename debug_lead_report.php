<?php

// Debug script để test LeadReport query
require_once 'vendor/autoload.php';

use App\Models\LeadReport;
use Illuminate\Support\Facades\DB;

// Khởi tạo Laravel app
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG LEAD REPORT QUERY ===\n\n";

// Test 1: Query trực tiếp từ model
echo "1. Query trực tiếp từ LeadReport model:\n";
$query1 = LeadReport::query()
    ->where('report_date_at', '>=', '2024-01-01')
    ->where('report_date_at', '<=', '2024-12-31');

echo "SQL: " . $query1->toSql() . "\n";
echo "Bindings: " . json_encode($query1->getBindings()) . "\n\n";

// Test 2: Query với newQuery()
echo "2. Query với newQuery():\n";
$model = new LeadReport();
$query2 = $model->newQuery()
    ->where('report_date_at', '>=', '2024-01-01')
    ->where('report_date_at', '<=', '2024-12-31');

echo "SQL: " . $query2->toSql() . "\n";
echo "Bindings: " . json_encode($query2->getBindings()) . "\n\n";

// Test 3: Query với conditions như trong repository
echo "3. Query với conditions như trong repository:\n";
$conditions = [
    'start_date_at' => '2024-01-01',
    'end_date_at' => '2024-12-31'
];

$query3 = $model->newQuery()
    ->when(isset($conditions['start_date_at']), function ($builder) use ($conditions) {
        $builder->where('report_date_at', '>=', $conditions['start_date_at']);
    })
    ->when(isset($conditions['end_date_at']), function ($builder) use ($conditions) {
        $builder->where('report_date_at', '<=', $conditions['end_date_at']);
    });

echo "SQL: " . $query3->toSql() . "\n";
echo "Bindings: " . json_encode($query3->getBindings()) . "\n\n";

// Test 4: Kiểm tra có global scope nào không
echo "4. Kiểm tra global scopes:\n";
$globalScopes = $model->getGlobalScopes();
echo "Global scopes: " . json_encode(array_keys($globalScopes)) . "\n\n";

// Test 5: Kiểm tra table name
echo "5. Kiểm tra table name:\n";
echo "Table: " . $model->getTable() . "\n\n";

// Test 6: Kiểm tra có soft deletes không
echo "6. Kiểm tra soft deletes:\n";
echo "Uses soft deletes: " . (method_exists($model, 'bootSoftDeletes') ? 'Yes' : 'No') . "\n\n";

echo "=== END DEBUG ===\n";
