<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

if (@parse_url(\Illuminate\Support\Facades\URL::current())['host'] !== 'apps.phongkhamcaokim.vn') {
    Route::group([
        'prefix' => 'common',
        'as' => 'common.',
    ], function () {
        Route::get('redirect/success', [\App\Http\Controllers\CommonController::class, 'redirectSuccess'])->name('redirect.success');

        Route::middleware(['auth', 'auth.session', 'auth.active'])->group(function () {
            // Logs.
            Route::get('logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index'])
                ->name('logs')
                ->middleware(['log_viewer.authorize']);

            // Files.
            Route::post('files', [\App\Http\Controllers\File\FileController::class, 'upload'])->name('files.upload');
        });
    });
}

Route::group([
    'middleware' => 'guest',
], function () {
    Route::get('login', [\App\Http\Controllers\AuthController::class, 'showLoginForm'])->name('auth.login');
    Route::post('login', [\App\Http\Controllers\AuthController::class, 'login']);
});



Route::group([
    'middleware' => ['auth', 'auth.session', 'auth.active'],
], function () {
    Route::get('/', [\App\Http\Controllers\AuthController::class, 'home'])->name('auth.home');

    if (@parse_url(\Illuminate\Support\Facades\URL::current())['host'] !== 'apps.phongkhamcaokim.vn') {
        Route::get('profile', [\App\Http\Controllers\ProfileController::class, 'edit'])->name('auth.profile');
        Route::put('profile', [\App\Http\Controllers\ProfileController::class, 'update']);

        Route::put('user-settings/set-show-real-money', [\App\Http\Controllers\UserSettingController::class, 'setShowRealMoney'])
            ->name('auth.user-settings.set-show-real-money');

        Route::get('password/confirm', [\App\Http\Controllers\AuthController::class, 'showConfirmPasswordForm'])->name('auth.password.confirm');
        Route::post('password/confirm', [\App\Http\Controllers\AuthController::class, 'confirmPassword']);

        Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
        Route::get('urgent-features', [\App\Http\Controllers\UrgentFeatureController::class, 'index'])
            ->name('urgent-features.index')->middleware(['password.confirm']);
        Route::post('urgent-features/download-backup', [\App\Http\Controllers\UrgentFeatureController::class, 'downloadBackup'])
            ->name('urgent-features.download_backup')->middleware(['password.confirm']);
        Route::post('urgent-features/remove-backup', [\App\Http\Controllers\UrgentFeatureController::class, 'removeBackup'])
            ->name('urgent-features.remove_backup')->middleware(['password.confirm']);

        // Users.
        Route::resource('users', \App\Http\Controllers\UserController::class)
            ->except('show', 'destroy');
        Route::post('users/{user}/inactivate', [\App\Http\Controllers\UserController::class, 'inactivate'])
            ->name('users.inactivate');
        Route::post('users/{user}/activate', [\App\Http\Controllers\UserController::class, 'activate'])
            ->name('users.activate');
        Route::get('users/get-telesales', [\App\Http\Controllers\UserController::class, 'getTelesalesByBusinessDepartmentId'])
            ->name('users.get_telesales_by_business_department');
        Route::get('users/get-receptionists', [\App\Http\Controllers\UserController::class, 'getReceptionistsByShopIds'])
            ->name('users.get_receptionists_by_shops');

        // My Manager - Người quản lý của tôi
        Route::get('my-manager', [\App\Http\Controllers\MyManagerController::class, 'index'])
            ->name('my-manager.index');
        // Route::get('my-manager/create', [\App\Http\Controllers\MyManagerController::class, 'create'])
        //     ->name('my-manager.create');
        // Route::post('my-manager', [\App\Http\Controllers\MyManagerController::class, 'store'])
        //     ->name('my-manager.store');
        // Route::delete('my-manager', [\App\Http\Controllers\MyManagerController::class, 'destroy'])
        //     ->name('my-manager.destroy');
        Route::get('my-manager/search', [\App\Http\Controllers\MyManagerController::class, 'search'])
            ->name('my-manager.search');
        Route::get('users/export-excel', [\App\Http\Controllers\UserController::class, 'exportExcel'])
            ->name('users.export-excel');

        // Users import.
        Route::get('users/import', [\App\Http\Controllers\UserImportController::class, 'form'])
            ->name('users.import');
        Route::post('users/import', [\App\Http\Controllers\UserImportController::class, 'import']);
        Route::get('users/import/sample', [\App\Http\Controllers\UserImportController::class, 'sample'])
            ->name('users.import.sample');

        // Timesheets
        Route::resource('timesheets', \App\Http\Controllers\TimesheetController::class)
            ->except('show');
        Route::get('timesheets/export', [\App\Http\Controllers\TimesheetController::class, 'export'])
            ->name('timesheets.export');
        Route::get('timesheets/get-user-info/{user}', [\App\Http\Controllers\TimesheetController::class, 'getUserInfo'])
            ->name('users.get_user_info');
        // Conversations
        Route::resource('conversations', \App\Http\Controllers\ConversationController::class)
            ->except('create', 'store', 'update', 'edit');
        Route::get('conversations/{conversation}/load-more-message-files', [\App\Http\Controllers\ConversationController::class, 'loadMoreMessageFiles'])
            ->name('conversations.load_more_message_files');

        // Bookings.
        Route::resource('bookings', \App\Http\Controllers\BookingController::class)
            ->except('show');
        Route::get('bookings/date', [\App\Http\Controllers\BookingController::class, 'indexDate'])
            ->name('bookings.index.date');
        Route::get('bookings/get-marketing-teams', [\App\Http\Controllers\BookingController::class, 'getMarketingTeamsByDepartment'])
            ->name('bookings.get-marketing-teams');
        Route::get('bookings/get-marketing-teams-for-head-revenue', [\App\Http\Controllers\BookingController::class, 'getMarketingTeamsByDepartmentForRevenueHead'])
            ->name('bookings.get-marketing-teams-for-head-revenue');
        Route::get('bookings/index-export-excel', [\App\Http\Controllers\BookingController::class, 'exportExcelIndex'])
            ->name('bookings.index-export-excel');
        Route::get('bookings/index-date-export-excel', [\App\Http\Controllers\BookingController::class, 'exportExcelIndexDate'])
            ->name('bookings.index-date-export-excel');
        Route::get('bookings/{booking}/show-audio', [\App\Http\Controllers\BookingController::class, 'showAudio'])
            ->name('bookings.show-audio');

        // Booking history
        Route::get('bookings/{booking}/histories', [\App\Http\Controllers\BookingHistoryController::class, 'index'])
            ->name('booking-histories.index');

        // Customer revenues.
        Route::resource('customer-revenues', \App\Http\Controllers\CustomerRevenueController::class)
            ->except('show');
        Route::get('customer-revenues/date', [\App\Http\Controllers\CustomerRevenueController::class, 'indexDate'])
            ->name('customer-revenues.index.date');
        Route::post('customer-revenues/debt', [\App\Http\Controllers\CustomerRevenueController::class, 'storeDebt'])
            ->name('customer-revenues.store.debt');

        // Revenue head evaluations
        Route::resource('revenue-head-evaluations', \App\Http\Controllers\RevenueHeadEvaluationController::class)
            ->only('index');
        Route::get('revenue-head-evaluations/load-more-revenue-heads', [\App\Http\Controllers\RevenueHeadEvaluationController::class, 'loadMoreRevenueHeads'])
            ->name('revenue-head-evaluations.load_more_revenue_heads');

        // Booking sources.
        Route::resource('booking-sources', \App\Http\Controllers\BookingSourceController::class)
            ->except('show');
        Route::post('booking-sources/{id}/restore', [\App\Http\Controllers\BookingSourceController::class, 'restore'])
            ->name('booking-sources.restore');

        // staff departments.
        Route::resource('staff-departments', \App\Http\Controllers\StaffDepartmentController::class)
            ->except('show');
        // Positions.
        Route::resource('positions', \App\Http\Controllers\PositionController::class)
            ->except('show');

        // Shifts.
        Route::resource('shifts', \App\Http\Controllers\ShiftController::class)
            ->except('show');

        // Shift Rule Histories.
        Route::resource('shift-rule-histories', \App\Http\Controllers\ShiftRuleHistoryController::class)
            ->except('show');
        Route::get('shift-rule-histories/by-shift/{shift}', [\App\Http\Controllers\ShiftRuleHistoryController::class, 'getByShift'])
            ->name('shift-rule-histories.by-shift');

        // Company addresses.
        Route::resource('company-addresses', \App\Http\Controllers\CompanyAddressController::class)
            ->except('show');

        // Shops.
        Route::resource('shops', \App\Http\Controllers\ShopController::class)
            ->except('show');
        Route::post('shops/{id}/restore', [\App\Http\Controllers\ShopController::class, 'restore'])
            ->name('shops.restore');

        // Teams.
        Route::resource('teams', \App\Http\Controllers\TeamController::class)
            ->except('show');
        Route::post('teams/{id}/restore', [\App\Http\Controllers\TeamController::class, 'restore'])
            ->name('teams.restore');

        // Main products.
        Route::resource('products', \App\Http\Controllers\ProductController::class)
            ->except('show');
        Route::post('products/{id}/restore', [\App\Http\Controllers\ProductController::class, 'restore'])
            ->name('products.restore');

        // Reports.
        Route::group([
            'prefix' => 'reports',
            'as' => 'reports.',
        ], function () {
            Route::get('revenue', [\App\Http\Controllers\ReportRevenueController::class, 'index'])->name('revenue');
            Route::get('revenue/date', [\App\Http\Controllers\ReportRevenueController::class, 'date'])->name('revenue.date');
            Route::get('revenue/month', [\App\Http\Controllers\ReportRevenueController::class, 'month'])->name('revenue.month');
            Route::get('revenue/customer', [\App\Http\Controllers\ReportRevenueController::class, 'customer'])->name('revenue.customer');
        });

        // Departments.
        Route::resource('departments', \App\Http\Controllers\DepartmentController::class)
            ->except('show');
        Route::post('departments/{id}/restore', [\App\Http\Controllers\DepartmentController::class, 'restore'])
            ->name('departments.restore');

        // Marketing Teams.
        Route::resource('marketing-teams', \App\Http\Controllers\MarketingTeamController::class)
            ->except('show');
        Route::post('marketing-teams/{id}/restore', [\App\Http\Controllers\MarketingTeamController::class, 'restore'])
            ->name('marketing-teams.restore');

        // Marketing team filters.
        Route::get('marketing-team-filters/{marketingTeamFilter}/search-marketing-teams', [\App\Http\Controllers\MarketingTeamFilterController::class, 'searchMarketingTeams']);
        Route::resource('marketing-team-filters', \App\Http\Controllers\MarketingTeamFilterController::class)
            ->except('show');

        // Lead Reports.
        Route::resource('lead-reports', \App\Http\Controllers\LeadReport\LeadReportController::class)
            ->except('show', 'delete');
        Route::get('lead-reports/search', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'index'])
            ->name('lead-reports.search.index');
        Route::get('lead-reports/search/marketing-teams', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'getMarketingTeamsByDepartment'])
            ->name('lead-reports.search.marketing-teams');
        Route::get('lead-reports/search/marketing-teams-by-business-department', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'getMarketingTeamsByBusinessDepartment'])
            ->name('lead-reports.search.marketing-teams-by-business-department');
        Route::get('lead-reports/search/doctors', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'getDoctorsByMarketingTeams'])
            ->name('lead-reports.search.doctors');
        Route::get('lead-reports/search-print', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'searchPrint'])
            ->name('lead-reports.search.print');
        Route::get('lead-reports/search-export-excel', [\App\Http\Controllers\LeadReport\LeadReportStatisticController::class, 'exportExcel'])
            ->name('lead-reports.search.export-excel');
        Route::get('lead-reports/comparison', [\App\Http\Controllers\LeadReport\ComparisonLeadReportController::class, 'index'])
            ->name('lead-reports-comparison.index');
        Route::get('lead-reports/comparison/get-shops-by-regions', [\App\Http\Controllers\LeadReport\ComparisonLeadReportController::class, 'getShopsByRegions'])
            ->name('lead-reports-comparison.get-shops-by-regions');

        // Lead Reports Doctor.
        Route::resource('lead-report-doctors', \App\Http\Controllers\LeadReportDoctor\LeadReportDoctorController::class)
            ->except('show', 'delete');

        // Fast Filter Lead Report
        Route::resource('fast-filter-lead-report', \App\Http\Controllers\FastFilterLeadReportController::class)
            ->only('store', 'destroy');

        // Customer.
        Route::get('customers/get-technician-doctor-with-shop/{shopId?}', [\App\Http\Controllers\UserController::class, 'getTechniciansAndDoctors'])
            ->name('get_technicians_doctors');
        Route::resource('customers', \App\Http\Controllers\CustomerController::class)
            ->only('index', 'create', 'store', 'edit', 'update', 'destroy');
        Route::get('customers/load-more-customers', [\App\Http\Controllers\CustomerController::class, 'loadMoreCustomers'])
            ->name('customers.load_more_customers');
        Route::get('customers/{id}/create/booking', [\App\Http\Controllers\CustomerController::class, 'createBooking'])->name('customers.create.booking');
        Route::post('customers/{id}/create/booking', [\App\Http\Controllers\CustomerController::class, 'storeBooking']);
        Route::get('customers/{id}/get/booking', [\App\Http\Controllers\CustomerController::class, 'getBooking'])->name('customers.get.booking');
        Route::get('customers/import', [\App\Http\Controllers\CustomerImportController::class, 'form'])
            ->name('customers.import');
        Route::post('customers/import', [\App\Http\Controllers\CustomerImportController::class, 'import']);
        Route::get('customers/import/sample', [\App\Http\Controllers\CustomerImportController::class, 'sample'])
            ->name('customers.import.sample');
        Route::get('customers/show/phone-number', [\App\Http\Controllers\CustomerController::class, 'showByPhoneNumber'])
            ->name('customers.show_by_phone_number');
        Route::get('customers/{customer}/customer-revenues', [\App\Http\Controllers\CustomerController::class, 'showCustomerRevenues'])
            ->name('customers.show.customer_revenues');
        Route::get('customers/{customer}/customer-revenues/debt', [\App\Http\Controllers\CustomerController::class, 'showCustomerDebt'])
            ->name('customers.show.customer_revenues.debt');
        Route::get('customers/{customer}/customer-revenues/deposit', [\App\Http\Controllers\CustomerController::class, 'showCustomerDeposit'])
            ->name('customers.show.customer_revenues.deposit');
        Route::get('customers/{id}/edit/booking/{bookingId}', [\App\Http\Controllers\CustomerController::class, 'editBooking'])->name('customers.edit.booking');
        Route::post('customers/{id}/edit/booking/{bookingId}', [\App\Http\Controllers\CustomerController::class, 'updateBooking'])->name('customers.update.booking');
        Route::get('customers/{id}/feedback', [\App\Http\Controllers\CustomerController::class, 'getCustomerFeedback'])->name('customers.get.feedback');
        Route::put('customers/{id}/feedback', [\App\Http\Controllers\CustomerController::class, 'updateSingleFieldFeedback'])->name('customers.update.feedback');

        // Doctor.
        Route::get('doctors/get-by-shop-id', [\App\Http\Controllers\DoctorController::class, 'getByShopId'])
            ->name('doctors.get_by_shop_id');
        Route::resource('doctors', \App\Http\Controllers\DoctorController::class)
            ->except('show');
        Route::post('doctors/{id}/restore', [\App\Http\Controllers\DoctorController::class, 'restore'])
            ->name('doctors.restore');

        // User action logs.
        Route::resource('user-action-logs', \App\Http\Controllers\UserActionLogController::class)
            ->only('index');

        // Roles.
        Route::resource('roles', \App\Http\Controllers\RoleController::class);

        // Notices.
        Route::resource('notices', \App\Http\Controllers\NoticeController::class)
            ->except('show');

        // Customer survey.
        Route::resource('customer-surveys', \App\Http\Controllers\CustomerSurveyController::class)
            ->except('show');
        Route::get('customer-surveys/export-excel', [\App\Http\Controllers\CustomerSurveyController::class, 'exportExcel'])
            ->name('customer-surveys.export-excel');

        // Sale Reports.
        Route::resource('sale-reports', \App\Http\Controllers\SaleReport\SaleReportController::class)
            ->except('show', 'delete');
        Route::get('sale-reports/statistic', [\App\Http\Controllers\SaleReport\SaleReportStatisticController::class, 'index'])
            ->name('sale-reports-statistic.index');
        Route::get('sale-reports/statistic/get-telesales', [\App\Http\Controllers\SaleReport\SaleReportStatisticController::class, 'getTelesalesByBusinessDepartmentIdWithTeamName'])
            ->name('sale-reports-statistic.get_telesales');
        Route::get('sale-reports/statistic/get-telesales-by-team', [\App\Http\Controllers\SaleReport\SaleReportStatisticController::class, 'getTelesalesByTeam'])
            ->name('sale-reports-statistic.get_telesales_by_team');

        // Shop kpi.
        Route::resource('shop-kpis', \App\Http\Controllers\ShopKpiController::class)->except('show');

        // Shop 2 kpi.
        Route::get('shop-2-kpis', [\App\Http\Controllers\Shop2KpiController::class, 'index'])->name('shop-2-kpis.index');
        Route::get('shop-2-kpis/create', [\App\Http\Controllers\Shop2KpiController::class, 'create'])->name('shop-2-kpis.create');
        Route::post('shop-2-kpis', [\App\Http\Controllers\Shop2KpiController::class, 'store'])->name('shop-2-kpis.store');
        Route::get('shop-2-kpis/{shop2Kpi}/edit', [\App\Http\Controllers\Shop2KpiController::class, 'edit'])->name('shop-2-kpis.edit');
        Route::put('shop-2-kpis/{shop2Kpi}', [\App\Http\Controllers\Shop2KpiController::class, 'update'])->name('shop-2-kpis.update');
        Route::delete('shop-2-kpis/{shop2Kpi}', [\App\Http\Controllers\Shop2KpiController::class, 'destroy'])->name('shop-2-kpis.destroy');

        // Marketing team kpi.
        Route::resource('marketing-team-kpis', \App\Http\Controllers\MarketingTeamKpiController::class)->except('show');

        // Kpi comparison chart.
        Route::get('kpi/comparison-chart', [\App\Http\Controllers\KpiChartController::class, 'index'])->name('kpi.comparison-chart');

        Route::middleware(['auth', 'auth.active'])->group(function () {
            Route::resource('ratings', \App\Http\Controllers\RatingController::class);

            // Rating Reports - Báo cáo đánh giá
            Route::get('rating-reports', [\App\Http\Controllers\RatingReportController::class, 'index'])
                ->name('rating-reports.index');
            Route::get('rating-reports/export', [\App\Http\Controllers\RatingReportController::class, 'export'])
                ->name('rating-reports.export');

        // Debug route for LeadReport query
        Route::get('debug/lead-report-query', function() {
            $conditions = [
                'start_date_at' => '2024-01-01',
                'end_date_at' => '2024-12-31'
            ];

            $model = new \App\Models\LeadReport();
            $query = $model->newQuery()
                ->when(isset($conditions['start_date_at']), function ($builder) use ($conditions) {
                    $builder->where('report_date_at', '>=', $conditions['start_date_at']);
                })
                ->when(isset($conditions['end_date_at']), function ($builder) use ($conditions) {
                    $builder->where('report_date_at', '<=', $conditions['end_date_at']);
                });

            $sql = $query->toSql();
            $bindings = $query->getBindings();
            $globalScopes = $model->getGlobalScopes();

            return response()->json([
                'sql' => $sql,
                'bindings' => $bindings,
                'global_scopes' => array_keys($globalScopes),
                'table' => $model->getTable(),
                'conditions' => $conditions,
                'uses_soft_deletes' => method_exists($model, 'bootSoftDeletes')
            ]);
        });

            // User Ratings - Đánh giá xếp hạng nhân viên
            Route::resource('user-ratings', \App\Http\Controllers\UserRatingController::class);
            Route::get('user-ratings/get-user-info/{user}', [\App\Http\Controllers\UserRatingController::class, 'getUserInfo'])
                ->name('user-ratings.get-user-info');

            // User Rating Reports - Báo cáo xếp hạng nhân viên
            Route::get('user-rating-reports', [\App\Http\Controllers\UserRatingReportController::class, 'index'])
                ->name('user-rating-reports.index');
            Route::get('user-rating-reports/export', [\App\Http\Controllers\UserRatingReportController::class, 'export'])
                ->name('user-rating-reports.export');
            Route::get('user-rating-reports/top-performers', [\App\Http\Controllers\UserRatingReportController::class, 'getTopPerformers'])
                ->name('user-rating-reports.top-performers');
        });

        // Attendance Explanation - Giải trình chấm công
        Route::get('attendance-explanation', [\App\Http\Controllers\AttendanceExplanationController::class, 'index'])
            ->name('attendance-explanation.index');
        Route::get('attendance-explanation/tagged-confirmations', [\App\Http\Controllers\AttendanceExplanationController::class, 'taggedConfirmations'])
            ->name('attendance-explanation.tagged-confirmations');
        Route::get('attendance-explanation/tagged-explanations', [\App\Http\Controllers\AttendanceExplanationController::class, 'getTaggedExplanations'])
            ->name('attendance-explanation.tagged-explanations');
        Route::get('attendance-explanation/tagged-history', [\App\Http\Controllers\AttendanceExplanationController::class, 'getTaggedHistory'])
            ->name('attendance-explanation.tagged-history');

        // Attendance Explanation Approval - Duyệt giải trình 2 bước (đặt trước {date} route)
        Route::get('attendance-explanation/manager-approval', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'managerIndex'])
            ->name('attendance-explanation.manager-approval');
        Route::get('attendance-explanation/hr-approval', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'hrIndex'])
            ->name('attendance-explanation.hr-approval');
        Route::get('attendance-explanation/user/{userId}/explanations', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'getUserExplanations'])
            ->name('attendance-explanation.user-explanations');
        Route::get('attendance-explanation/managed-employees', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'getAllManagedEmployees'])
            ->name('attendance-explanation.managed-employees');
        Route::get('attendance-explanation/manager-history-by-month', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'getManagerHistoryByMonth'])
            ->name('attendance-explanation.manager-history-by-month');
        Route::get('attendance-explanation/hr-history-by-month', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'getHrHistoryByMonth'])
            ->name('attendance-explanation.hr-history-by-month');
        Route::post('attendance-explanation/{attendanceExplanation}/manager-approve', [\App\Http\Controllers\AttendanceExplanationController::class, 'managerApprove'])
            ->name('attendance-explanation.manager-approve');
        Route::post('attendance-explanation/bulk-manager-approve', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'bulkManagerApprove'])
            ->name('attendance-explanation.bulk-manager-approve');
        Route::post('attendance-explanation/{attendanceExplanation}/hr-approve', [\App\Http\Controllers\AttendanceExplanationController::class, 'hrApprove'])
            ->name('attendance-explanation.hr-approve');
        Route::post('attendance-explanation/bulk-hr-approve', [\App\Http\Controllers\AttendanceExplanationApprovalController::class, 'bulkHrApprove'])
            ->name('attendance-explanation.hr-bulk-approve');
        Route::post('attendance-explanation/bulk-tagged-confirm', [\App\Http\Controllers\AttendanceExplanationController::class, 'bulkTaggedConfirm'])
            ->name('attendance-explanation.tagged-bulk-confirm');
        Route::post('attendance-explanation/{attendanceExplanation}/tagged-user-confirm', [\App\Http\Controllers\AttendanceExplanationController::class, 'taggedUserConfirm'])
            ->name('attendance-explanation.tagged-user-confirm');

        // Các route có parameter {date} đặt sau để tránh conflict
        Route::get('attendance-explanation/api/{date}', [\App\Http\Controllers\AttendanceExplanationController::class, 'getAttendanceInfo'])
            ->where('date', '[0-9]{4}-[0-9]{2}-[0-9]{2}')
            ->name('attendance-explanation.api');
        Route::get('attendance-explanation/tagged-explanations', [\App\Http\Controllers\AttendanceExplanationController::class, 'getTaggedExplanations'])
            ->name('attendance-explanation.tagged-explanations');
        Route::post('attendance-explanation/{attendanceExplanation}/tagged-user-confirm', [\App\Http\Controllers\AttendanceExplanationController::class, 'taggedUserConfirm'])
            ->name('attendance-explanation.tagged-user-confirm');
        Route::get('attendance-explanation/{attendanceExplanation}/edit', [\App\Http\Controllers\AttendanceExplanationController::class, 'edit'])
            ->name('attendance-explanation.edit');
        Route::put('attendance-explanation/{attendanceExplanation}', [\App\Http\Controllers\AttendanceExplanationController::class, 'update'])
            ->name('attendance-explanation.update');
        Route::get('attendance-explanation/{date}', [\App\Http\Controllers\AttendanceExplanationController::class, 'show'])
            ->where('date', '[0-9]{4}-[0-9]{2}-[0-9]{2}')
            ->name('attendance-explanation.show');
        Route::post('attendance-explanation', [\App\Http\Controllers\AttendanceExplanationController::class, 'store'])
            ->name('attendance-explanation.store');
        Route::delete('attendance-explanation/{attendanceExplanation}', [\App\Http\Controllers\AttendanceExplanationController::class, 'destroy'])
            ->name('attendance-explanation.destroy');
        Route::patch('attendance-explanation/{attendanceExplanation}/pause', [\App\Http\Controllers\AttendanceExplanationController::class, 'pause'])
            ->name('attendance-explanation.pause');
        Route::patch('attendance-explanation/{attendanceExplanation}/resume', [\App\Http\Controllers\AttendanceExplanationController::class, 'resume'])
            ->name('attendance-explanation.resume');

        // Logout
        Route::any('logout', [\App\Http\Controllers\AuthController::class, 'logout'])->name('auth.logout');
    }
});

// Passport.
Route::group([
    'as' => 'passport.',
    'prefix' => config('passport.path', 'oauth'),
    'namespace' => '\Laravel\Passport\Http\Controllers',
], function () {
    Route::get('authorize', [
        'uses' => 'AuthorizationController@authorize',
        'as' => 'authorizations.authorize',
    ]);

    Route::group([
        'middleware' => ['auth', 'auth.active'],
    ], function () {
        Route::post('authorize', [
            'uses' => 'ApproveAuthorizationController@approve',
            'as' => 'authorizations.approve',
        ]);

        Route::delete('authorize', [
            'uses' => 'DenyAuthorizationController@deny',
            'as' => 'authorizations.deny',
        ]);
    });
});
