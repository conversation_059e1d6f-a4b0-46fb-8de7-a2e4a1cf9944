# Quy Tắc Phạt x2 Khi Vi Phạm > 7 Lần/Tháng

## Tổng quan
Đã thêm quy tắc phạt x2 số tiền phạt nếu nhân viên vi phạm trên 7 lần trong tháng.

## Quy tắc áp dụng

### 1. **Điều kiện kích hoạt:**
- **Số vi phạm trong tháng > 7 lần**
- **Vi phạm**: Đi muộn hoặc về sớm
- **Không tính**: Những ngày có giải trình đượ<PERSON> du<PERSON> (`final_status = 'approved'`)

### 2. **Cách tính:**
- **Bước 1**: Tính phạt bình thường theo bảng phạt
- **Bước 2**: Nếu tổng vi phạm tháng > 7 → Nhân x2
- **Bước 3**: <PERSON><PERSON><PERSON> nhật trạng thái hiển thị

### 3. **<PERSON><PERSON> dụ:**
```
Nhân viên đi muộn 15 phút:
- <PERSON><PERSON><PERSON> bình thường: 100,000 VNĐ
- Nếu đã vi phạm > 7 lần trong tháng: 200,000 VNĐ
```

## Logic implementation

### 1. **Method calculateAttendanceData() (dòng 260-308)**
```php
// Xử lý giải trình nếu có
if ($explanations && $explanations->count() > 0) {
    $data = $this->applyExplanations($data, $explanations, $timesheet);
} else {
    // Tính vi phạm nếu không có giải trình
    $data = $this->calculateViolations($data, $timesheet, $isChief);
    
    // Áp dụng quy tắc x2 nếu vi phạm > 7 lần trong tháng
    $data = $this->applyMonthlyViolationMultiplier($data, $timesheet);
}
```

### 2. **Method applyMonthlyViolationMultiplier() (dòng 449-472)**
```php
private function applyMonthlyViolationMultiplier($data, $timesheet)
{
    // Chỉ áp dụng nếu có vi phạm trong ngày hiện tại
    if ($data['violation_count'] == 0) {
        return $data;
    }

    // Tính tổng số vi phạm trong tháng
    $monthlyViolations = $this->getMonthlyViolationCount($timesheet->user_id, $timesheet->date);
    
    // Nếu > 7 lần vi phạm trong tháng thì x2 phạt tiền
    if ($monthlyViolations > 7) {
        $data['late_penalty'] *= 2;
        $data['early_penalty'] *= 2;
        $data['total_penalty'] = $data['late_penalty'] + $data['early_penalty'];
        
        // Cập nhật trạng thái để hiển thị đã áp dụng x2
        if ($data['attendance_status'] === 'Có vi phạm') {
            $data['attendance_status'] = 'Có vi phạm (x2 - >7 lần/tháng)';
        }
    }

    return $data;
}
```

### 3. **Method getMonthlyViolationCount() (dòng 474-508)**
```php
private function getMonthlyViolationCount($userId, $date)
{
    $startOfMonth = Carbon::parse($date)->startOfMonth();
    $endOfMonth = Carbon::parse($date)->endOfMonth();
    
    // Lấy tất cả timesheet trong tháng
    $timesheets = \App\Models\Timesheet::where('user_id', $userId)
        ->whereBetween('date', [$startOfMonth, $endOfMonth])
        ->whereNotNull('checkin')
        ->whereNotNull('checkout')
        ->with('shift')
        ->get();
        
    $violationCount = 0;
    
    foreach ($timesheets as $timesheet) {
        // Kiểm tra có giải trình được duyệt không
        $hasApprovedExplanation = AttendanceExplanation::where('user_id', $userId)
            ->whereDate('date', $timesheet->date)
            ->where('final_status', 'approved')
            ->exists();
            
        // Nếu có giải trình được duyệt thì bỏ qua
        if ($hasApprovedExplanation) {
            continue;
        }
        
        // Kiểm tra vi phạm
        if ($this->hasViolation($timesheet)) {
            $violationCount++;
        }
    }
    
    return $violationCount;
}
```

### 4. **Method hasViolation() (dòng 510-527)**
```php
private function hasViolation($timesheet)
{
    if (!$timesheet->shift || !$timesheet->checkin || !$timesheet->checkout) {
        return false;
    }
    
    $checkin = Carbon::parse($timesheet->checkin);
    $checkout = Carbon::parse($timesheet->checkout);
    $timesheetDate = Carbon::parse($timesheet->date);
    $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift->start_time);
    $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift->end_time);
    
    // Có vi phạm nếu đi muộn hoặc về sớm
    return $checkin->gt($expectedCheckin) || $checkout->lt($expectedCheckout);
}
```

## Bảng phạt cập nhật

### **Nhân viên (staff):**
| Vi phạm | Phạt thường | Phạt x2 (>7 lần/tháng) |
|---------|-------------|-------------------------|
| 1-10 phút | 50,000 | 100,000 |
| 11-30 phút | 100,000 | 200,000 |
| 31-60 phút | 120,000 | 240,000 |
| 61-120 phút | 200,000 | 400,000 |
| >120 phút | 200,000 | 400,000 |

### **Trưởng nhóm (chief):**
| Vi phạm | Phạt thường | Phạt x2 (>7 lần/tháng) |
|---------|-------------|-------------------------|
| 1-10 phút | 50,000 | 100,000 |
| 11-30 phút | 100,000 | 200,000 |
| 31-60 phút | 150,000 | 300,000 |
| 61-120 phút | 250,000 | 500,000 |
| >120 phút | 250,000 | 500,000 |

## Hiển thị trong Excel

### **Cột "Xác nhận tình trạng chấm công":**
- **Bình thường**: "Bình thường"
- **Vi phạm thường**: "Có vi phạm"
- **Vi phạm x2**: "Có vi phạm (x2 - >7 lần/tháng)"

### **Cột "Tổng tiền phạt":**
- Hiển thị số tiền đã nhân x2 nếu áp dụng

## Performance considerations

### 1. **Query optimization:**
- Sử dụng `with('shift')` để eager load
- Filter theo tháng để giảm dữ liệu
- Cache kết quả nếu cần

### 2. **Memory usage:**
- Chỉ load timesheet cần thiết
- Không load toàn bộ dữ liệu user

### 3. **Database queries:**
- 1 query để lấy timesheets trong tháng
- N queries để check explanations (có thể optimize)

## Test cases

### **Case 1: ≤ 7 vi phạm/tháng**
- Phạt bình thường
- Trạng thái: "Có vi phạm"

### **Case 2: > 7 vi phạm/tháng**
- Phạt x2
- Trạng thái: "Có vi phạm (x2 - >7 lần/tháng)"

### **Case 3: Có giải trình được duyệt**
- Không tính vào số vi phạm tháng
- Không áp dụng x2

## Lưu ý

### 1. **Tính toán theo tháng dương lịch:**
- Từ ngày 1 đến cuối tháng
- Reset mỗi tháng mới

### 2. **Chỉ tính vi phạm thực tế:**
- Có checkin/checkout
- Không có giải trình được duyệt
- Đi muộn hoặc về sớm so với ca

### 3. **Áp dụng cho tất cả vi phạm:**
- Cả đi muộn và về sớm
- Trong cùng ngày có thể có 2 vi phạm

## Mở rộng tương lai

### 1. **Cấu hình linh hoạt:**
- Số lần vi phạm threshold (hiện tại: 7)
- Hệ số nhân (hiện tại: x2)
- Lưu trong database config

### 2. **Báo cáo vi phạm:**
- Dashboard hiển thị số vi phạm/tháng
- Cảnh báo khi gần đạt threshold

### 3. **Quy tắc phức tạp hơn:**
- x3 cho >15 vi phạm
- Phạt theo quý, năm
- Phạt theo loại vi phạm

## Kết luận

✅ **Đã implement:** Quy tắc phạt x2 khi vi phạm > 7 lần/tháng

🎯 **Kết quả:** Export timesheets hiển thị đúng mức phạt x2 cho những trường hợp vi phạm nhiều

🔧 **Logic:** Tính tổng vi phạm tháng → Áp dụng x2 → Cập nhật trạng thái hiển thị
