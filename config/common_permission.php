<?php

return [
    'permission' => [
        \App\Enums\Permission\GroupEnum::Booking => [
            'viewAny' => 'Xem danh sách lịch hẹn',
            'create' => 'Tạo lịch hẹn',
            'update' => 'Sửa lịch hẹn',
            'delete' => 'Xóa lịch hẹn',
            'createReExamination' => 'Tạo lịch tái khám',
            'editReExamination' => 'Sửa lịch tái khám',
            'exportBooking' => 'Export lịch hẹn',
            'viewRevenueHeadEvaluation' => 'Xem danh sách đánh giá đầu hóa đơn/tệp khách',
            'listenBookingAudio' => 'Nghe file ghi âm',
            'viewBookingLast2Months' => 'Xem lịch hẹn 2 tháng gần nhất',
            'limitByMktTeam' => 'Giới hạn theo team mkt',
            'limitByShop' => 'Giới hạn theo chi nhánh',
        ],
        \App\Enums\Permission\GroupEnum::BookingSource => [
            'viewAny' => 'Xem danh sách nguồn',
            'create' => 'Tạo nguồn',
            'update' => 'Sửa nguồn',
            'delete' => 'Xóa nguồn',
            'restore' => 'Khôi phục nguồn',
        ],
        \App\Enums\Permission\GroupEnum::BookingHistory => [
            'viewAny' => 'Xem danh sách lịch sử lịch hẹn',
            'create' => 'Tạo lịch sử lịch hẹn',
        ],
        \App\Enums\Permission\GroupEnum::StaffDepartment => [
            'viewAny' => 'Xem danh sách phòng ban',
            'create' => 'Tạo phòng ban',
            'update' => 'Sửa phòng ban',
            'delete' => 'Xóa phòng ban',
        ],
        \App\Enums\Permission\GroupEnum::CompanyAddress => [
            'viewAny' => 'Xem danh sách địa chỉ công ty',
            'create' => 'Tạo địa chỉ công ty',
            'update' => 'Sửa địa chỉ công ty',
            'delete' => 'Xóa địa chỉ công ty',
        ],
        \App\Enums\Permission\GroupEnum::Customer => [
            'viewAny' => 'Xem danh sách khách hàng',
            'viewPhoneNumber' => 'Xem số điện thoại khách hàng (full số)',
            'viewPhoneNumberFirstFiveHidden' => 'Xem số điện thoại khách hàng (che 5 số đầu)',
            'viewCustomerRevenues' => 'Xem doanh thu từ khách hàng',
            'create' => 'Tạo khách hàng',
            'update' => 'Sửa khách hàng',
            'import' => 'Import khách hàng',
            'updatePhoneNumber' => 'Sửa số điện thoại',
            'viewIdentityNumberOnlyLastFour' => 'Chỉ xem 4 số cuối CCCD',
            'limitByShop' => 'Giới hạn khách hàng theo chi nhánh',
        ],
        \App\Enums\Permission\GroupEnum::CustomerRevenue => [
            'viewAny' => 'Xem danh sách hóa đơn',
            'create' => 'Tạo hóa đơn',
            'update' => 'Sửa hóa đơn',
            'delete' => 'Xóa hóa đơn tại mọi thời điểm',
            'deleteIn24Hours' => 'Xóa hóa đơn trong 24 giờ',
            'viewCustomerRevenueLast2Months' => 'Xem hóa đơn 2 tháng gần nhất',
            'limitByMktTeam' => 'Giới hạn theo team mkt',
            'limitByShop' => 'Giới hạn theo chi nhánh',
            'limitByDoctor' => 'Giới hạn theo bác sĩ',
        ],
        \App\Enums\Permission\GroupEnum::Shop => [
            'viewAny' => 'Xem danh sách chi nhánh',
            'create' => 'Tạo chi nhánh',
            'update' => 'Sửa chi nhánh',
            'delete' => 'Xóa chi nhánh',
            'restore' => 'Khôi phục chi nhánh',
//            'requireSpecificShops' => 'Bị giới hạn chi nhánh',
//            'requireSpecificShopsToBooking' => 'Bị giới hạn chi nhánh với lịch hẹn',
        ],
        \App\Enums\Permission\GroupEnum::Team => [
            'viewAny' => 'Xem danh sách team',
            'create' => 'Tạo team',
            'update' => 'Sửa team',
            'delete' => 'Xóa team',
            'restore' => 'Khôi phục team',
        ],
        \App\Enums\Permission\GroupEnum::User => [
            'viewAny' => 'Xem danh sách nhân viên',
            'create' => 'Tạo nhân viên',
            'update' => 'Sửa nhân viên',
            'delete' => 'Xóa nhân viên',
            'import' => 'Import nhân viên',
            'activate' => 'On/off nhân viên',
            'setShowRealMoney' => 'On/off xem giá trị thực',
        ],
        \App\Enums\Permission\GroupEnum::Timesheet => [
            'viewAny' => 'Xem danh sách bảng chấm công',
            'create' => 'Tạo bảng chấm công',
            'update' => 'Sửa bảng chấm công',
            'delete' => 'Xóa bảng chấm công',
            'export' => 'Export bảng chấm công',
        ],
        \App\Enums\Permission\GroupEnum::Report => [
            'viewRevenue' => 'Xem báo cáo doanh thu',
        ],
        \App\Enums\Permission\GroupEnum::Product => [
            'viewAny' => 'Xem danh sách dịch vụ',
            'create' => 'Tạo dịch vụ',
            'update' => 'Sửa dịch vụ',
            'delete' => 'Xóa dịch vụ',
            'restore' => 'Khôi phục dịch vụ',
        ],
        \App\Enums\Permission\GroupEnum::Department => [
            'viewAny' => 'Xem danh sách bộ phận',
            'create' => 'Tạo bộ phận',
            'update' => 'Sửa bộ phận',
            'delete' => 'Xóa bộ phận',
            'restore' => 'Khôi phục bộ phận',
        ],
        \App\Enums\Permission\GroupEnum::MarketingTeam => [
            'viewAny' => 'Xem danh sách team MKT',
            'create' => 'Tạo team MKT',
            'update' => 'Sửa team MKT',
            'delete' => 'Xóa team MKT',
            'restore' => 'Khôi phục team MKT',
        ],
        \App\Enums\Permission\GroupEnum::MarketingTeamFilter => [
            'viewAny' => 'Xem danh sách bộ lọc team MKT',
            'create' => 'Tạo bộ lọc team MKT',
            'update' => 'Sửa bộ lọc team MKT',
            'delete' => 'Xóa bộ lọc team MKT',
        ],
        \App\Enums\Permission\GroupEnum::LeadReport => [
            'viewAny' => 'Xem danh sách báo cáo LEAD',
            'create' => 'Tạo báo cáo LEAD',
            'viewAndCreateAndUpdateLimitByShop' => 'Xem/tạo/sửa báo cáo LEAD giới hạn theo chi nhánh',
            'update' => 'Sửa báo cáo LEAD',
            'viewReportDepartment' => 'Xem báo cáo LEAD bộ phận',
            'viewReportProduct' => 'Xem báo cáo LEAD dịch vụ',
            'viewReportAll' => 'Xem báo cáo LEAD tổng hợp',
            'viewReportDoctorAll' => 'Xem báo cáo LEAD bác sĩ',
            'viewReportService' => 'Xem BC Lead dịch vụ (Rating)',
            'viewComparisonReportShop' => 'Xem so sánh báo cáo LEAD theo chi nhánh',
            'viewComparisonReportMarketingTeam' => 'Xem so sánh báo cáo LEAD theo team MTK',
            'viewComparisonReportBusinessDepartment' => 'Xem so sánh báo cáo LEAD theo phòng kinh doanh',
            'viewReportLast2Months' => 'Xem báo cáo LEAD 2 tháng gần nhất',
            'limitByMktTeam' => 'Giới hạn theo team mkt',
            'limitByShop' => 'Giới hạn theo chi nhánh',
        ],
        \App\Enums\Permission\GroupEnum::FastFilterLeadReport => [
            'create' => 'Tạo bộ lọc nhanh',
            'delete' => 'Xóa bộ lọc nhanh'
        ],
        \App\Enums\Permission\GroupEnum::LeadReportDoctor => [
            'viewAny' => 'Xem danh sách báo cáo LEAD bác sĩ',
            'create' => 'Tạo báo cáo LEAD bác sĩ',
            'update' => 'Sửa báo cáo LEAD bác sĩ',
        ],
        \App\Enums\Permission\GroupEnum::Doctor => [
            'viewAny' => 'Xem danh sách bác sĩ',
            'create' => 'Thêm bác sĩ',
            'update' => 'Sửa bác sĩ',
            'delete' => 'Xóa bác sĩ',
            'restore' => 'Khôi phục bác sĩ',
            'requireSpecificDoctors' => 'Bị giới hạn bác sĩ (Toàn hệ thống)',
            'requireSpecificDoctorsToBooking' => 'Bị giới hạn bác sĩ với lịch hẹn',
            'requireSpecificDoctorsToLeadReport' => 'Bị giới hạn bác sĩ với lead report',
        ],
        \App\Enums\Permission\GroupEnum::Shift => [
            'viewAny' => 'Xem danh sách ca làm việc',
            'create' => 'Tạo ca làm việc',
            'update' => 'Sửa ca làm việc',
            'delete' => 'Xóa ca làm việc',
        ],
        \App\Enums\Permission\GroupEnum::Position => [
            'viewAny' => 'Xem danh sách chức vụ',
            'create' => 'Tạo chức vụ',
            'update' => 'Sửa chức vụ',
            'delete' => 'Xóa chức vụ',
        ],
        \App\Enums\Permission\GroupEnum::Role => [
            'viewAny' => 'Xem danh sách phân quyền',
            'create' => 'Tạo phân quyền',
            'update' => 'Sửa phân quyền',
            'delete' => 'Xóa phân quyền',
        ],
        \App\Enums\Permission\GroupEnum::Notice => [
            'viewAny' => 'Xem danh sách thông báo',
            'create' => 'Tạo thông báo',
            'update' => 'Sửa thông báo',
            'delete' => 'Xóa thông báo',
        ],
        \App\Enums\Permission\GroupEnum::CustomerSurvey => [
            'viewAny' => 'Xem danh sách hiệu quả & thái độ',
            'create' => 'Tạo hiệu quả & thái độ',
            'update' => 'Sửa hiệu quả & thái độ',
            'delete' => 'Xóa hiệu quả & thái độ',
            'limitByShop' => 'Giới hạn theo chi nhánh',
        ],
        \App\Enums\Permission\GroupEnum::SaleReport => [
            'viewAny' => 'Xem danh sách báo cáo SALE',
            'create' => 'Tạo báo cáo SALE',
            'update' => 'Sửa báo cáo SALE',
            'viewReport' => 'Xem thống kê báo cáo SALE',
            'restrictAsSalesman' => 'Bị hạn chế như là một telesale (Chỉ tương tác với report được gán telesale chính là bản thân)',
            'restrictAsSalesmanManager' => 'Bị hạn chế như là một quản lý telesale (Chỉ tương tác với report được gán telesale là nhân viên mình quản lý)',
        ],
        \App\Enums\Permission\GroupEnum::ShopKpi => [
            'viewAny' => 'Xem danh sách kpi chi nhánh',
            'create' => 'Tạo kpi chi nhánh',
            'update' => 'Sửa kpi chi nhánh',
            'delete' => 'Xóa kpi chi nhánh',
        ],
        \App\Enums\Permission\GroupEnum::MarketingTeamKpi => [
            'viewAny' => 'Xem danh sách kpi team MKT',
            'create' => 'Tạo kpi team MKT',
            'update' => 'Sửa kpi team MKT',
            'delete' => 'Xóa kpi team MKT',
        ],
        \App\Enums\Permission\GroupEnum::Kpi => [
            'viewKpiChartShop' => 'Xem so sánh Kpi chi nhánh',
            'viewKpiChartMarketingTeam' => 'Xem so sánh Kpi team MKT',
        ],
        \App\Enums\Permission\GroupEnum::Shop2Kpi => [
            'manage' => 'Nhập KPI doanh thu, cpqc',
        ],
        \App\Enums\Permission\GroupEnum::Rating => [
            'viewAny' => 'Xem danh sách đánh giá',
            'create' => 'Tạo đánh giá',
            'update' => 'Sửa đánh giá',
            'delete' => 'Xóa đánh giá',
            'verify' => 'Xác thực đánh giá',
            'updateIn24Hours' => 'Sửa đánh giá trong 24 giờ',
            'deleteIn24Hours' => 'Xóa đánh giá trong 24 giờ',
            'viewStatistics' => 'Xem thống kê đánh giá',
        ],
        \App\Enums\Permission\GroupEnum::UserRating => [
            'viewAny' => 'Xem danh sách đánh giá xếp hạng nhân viên',
            'view' => 'Xem chi tiết đánh giá xếp hạng nhân viên',
            'create' => 'Tạo đánh giá xếp hạng nhân viên',
            'update' => 'Sửa đánh giá xếp hạng nhân viên',
            'delete' => 'Xóa đánh giá xếp hạng nhân viên',
            'viewAny user_rating_report' => 'Xem báo cáo xếp hạng nhân viên',
            'export user_rating_report' => 'Xuất báo cáo xếp hạng nhân viên',
        ],
        \App\Enums\Permission\GroupEnum::ShiftRuleHistory => [
            'viewAny' => 'Xem danh sách quy tắc ca làm việc',
            'create' => 'Tạo quy tắc ca làm việc',
            'update' => 'Sửa quy tắc ca làm việc',
            'delete' => 'Xóa quy tắc ca làm việc',
        ],
    ],
    'role_permission' => [
        \App\Enums\Role\RoleEnum::SuperAdministrator => array_merge(
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'import',
                'activate',
                'setShowRealMoney',
            ], \App\Enums\Permission\GroupEnum::User),
            permission_names([
                'viewAny',
                'create',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Team),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Shop),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::BookingSource),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::StaffDepartment),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CompanyAddress),
            permission_names([
                'viewAny',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Booking),
            permission_names([
                'viewAny',
                'viewPhoneNumber',
                'viewCustomerRevenues',
                'create',
                'update',
                'import',
                'updatePhoneNumber',
            ], \App\Enums\Permission\GroupEnum::Customer),
            permission_names([
                'viewRevenue',
            ], \App\Enums\Permission\GroupEnum::Report),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Product),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Department),
            permission_names([
                'viewAny',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CustomerRevenue),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::MarketingTeam),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::MarketingTeamFilter),
            permission_names([
                'viewAny',
                'update',
                'viewReportDepartment',
                'viewReportProduct',
                'viewReportAll',
                'viewReportDoctorAll',
                'viewReportService',
                'viewComparisonReportShop',
                'viewComparisonReportMarketingTeam',
                'viewComparisonReportBusinessDepartment',
            ], \App\Enums\Permission\GroupEnum::LeadReport),
            permission_names([
                'create',
                'delete',
            ], \App\Enums\Permission\GroupEnum::FastFilterLeadReport),
            permission_names([
                'viewAny',
                'update',
            ], \App\Enums\Permission\GroupEnum::LeadReportDoctor),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Doctor),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Shift),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Position),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Role),
            permission_names([
                'viewAny',
            ], \App\Enums\Permission\GroupEnum::BookingHistory),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Notice),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CustomerSurvey),
            permission_names([
                'viewAny',
                'update',
                'viewReport',
            ], \App\Enums\Permission\GroupEnum::SaleReport),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::ShopKpi),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::MarketingTeamKpi),
            permission_names([
                'viewKpiChartShop',
                'viewKpiChartMarketingTeam',
            ], \App\Enums\Permission\GroupEnum::Kpi),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'verify',
                'updateIn24Hours',
                'deleteIn24Hours',
                'viewStatistics',
            ], \App\Enums\Permission\GroupEnum::Rating),
            permission_names([
                'viewAny',
                'view',
                'create',
                'update',
                'delete',
                'viewAny user_rating_report',
                'export user_rating_report',
            ], \App\Enums\Permission\GroupEnum::UserRating),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::ShiftRuleHistory),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'export',
            ], \App\Enums\Permission\GroupEnum::Timesheet)
        ),
        \App\Enums\Role\RoleEnum::Administrator => array_merge(
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'import',
                'activate',
                'setShowRealMoney',
            ], \App\Enums\Permission\GroupEnum::User),
            permission_names([
                'viewAny',
                'create',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Team),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Shop),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::BookingSource),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::StaffDepartment),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CompanyAddress),
            permission_names([
                'viewAny',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Booking),
            permission_names([
                'viewAny',
                'viewPhoneNumber',
                'viewCustomerRevenues',
                'create',
                'update',
                'import',
                'updatePhoneNumber',
            ], \App\Enums\Permission\GroupEnum::Customer),
            permission_names([
                'viewRevenue',
            ], \App\Enums\Permission\GroupEnum::Report),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Product),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Department),
            permission_names([
                'viewAny',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CustomerRevenue),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::MarketingTeam),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::MarketingTeamFilter),
            permission_names([
                'viewAny',
                'update',
                'viewReportDepartment',
                'viewReportProduct',
                'viewReportAll',
                'viewReportDoctorAll',
                'viewReportService',
                'viewComparisonReportShop',
                'viewComparisonReportMarketingTeam',
                'viewComparisonReportBusinessDepartment',
            ], \App\Enums\Permission\GroupEnum::LeadReport),
            permission_names([
                'create',
                'delete',
            ], \App\Enums\Permission\GroupEnum::FastFilterLeadReport),
            permission_names([
                'viewAny',
                'update',
            ], \App\Enums\Permission\GroupEnum::LeadReportDoctor),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'restore',
            ], \App\Enums\Permission\GroupEnum::Doctor),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Shift),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Position),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Role),
            permission_names([
                'viewAny',
            ], \App\Enums\Permission\GroupEnum::BookingHistory),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::Notice),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::CustomerSurvey),
            permission_names([
                'viewAny',
                'update',
                'viewReport',
            ], \App\Enums\Permission\GroupEnum::SaleReport),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::ShopKpi),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::MarketingTeamKpi),
            permission_names([
                'viewKpiChartShop',
                'viewKpiChartMarketingTeam',
            ], \App\Enums\Permission\GroupEnum::Kpi),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'verify',
                'updateIn24Hours',
                'deleteIn24Hours',
                'viewStatistics',
            ], \App\Enums\Permission\GroupEnum::Rating),
            permission_names([
                'viewAny',
                'view',
                'create',
                'update',
                'delete',
                'viewAny user_rating_report',
                'export user_rating_report',
            ], \App\Enums\Permission\GroupEnum::UserRating),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
            ], \App\Enums\Permission\GroupEnum::ShiftRuleHistory),
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'export',
            ], \App\Enums\Permission\GroupEnum::Timesheet)
        ),
        \App\Enums\Role\RoleEnum::ChiefCustomerOfficer => array_merge(
            permission_names([
                'viewAny',
                'viewReportDoctorAll',
                'viewReportAll',
            ], \App\Enums\Permission\GroupEnum::LeadReport),
            permission_names([
                'viewAny',
            ], \App\Enums\Permission\GroupEnum::LeadReportDoctor),
        ),
        \App\Enums\Role\RoleEnum::QualitySupervisor => array_merge(
            permission_names([
                'viewAny',
                'create',
                'update',
                'delete',
                'verify',
                'updateIn24Hours',
                'deleteIn24Hours',
                'viewStatistics',
            ], \App\Enums\Permission\GroupEnum::Rating),
            permission_names([
                'viewReportService',
            ], \App\Enums\Permission\GroupEnum::LeadReport),
        ),
    ],
];
