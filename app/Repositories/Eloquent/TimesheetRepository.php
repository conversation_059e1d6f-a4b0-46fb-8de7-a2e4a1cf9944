<?php

namespace App\Repositories\Eloquent;

use App\Contracts\Repositories\TimesheetRepositoryInterface;
use App\Models\Timesheet;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TimesheetRepository extends BaseRepository implements TimesheetRepositoryInterface
{
    public function model(): string
    {
        return Timesheet::class;
    }

    public function getPaginator(int $perPage, array $filters = []): Paginator
    {
        return $this->queryListBase($filters)
            ->with(['user.staffDepartment', 'shift', 'companyAddress'])
            ->orderBy('id', 'desc')
            ->paginate($perPage);
    }

    public function queryToExport(array $conditions = []): Builder
    {
        return $this->queryListBase($conditions)
            ->select([
                DB::raw('COUNT(timesheets.id) as timsheets_unique_date_user_id_count'),
                'timesheets.user_id',
                'users.staff_department_id as user_department_id',
                'users.name as user_name',
                'users.code as user_code',
                'users.staff_department_chief_flag as user_department_chief_flag',
                'staff_departments.name as user_department',
                'timesheets.date',
                DB::raw('MIN(timesheets.checkin) as checkin'),
                DB::raw('MAX(timesheets.checkout) as checkout'),
                DB::raw('MIN(shifts.name) as shift_name'),
                // Ưu tiên lấy từ shift_rule_histories nếu có, fallback về shifts
                DB::raw('COALESCE(MIN(shift_rule_histories.start_time), MIN(shifts.start_time)) as shift_start_time'),
                DB::raw('COALESCE(MAX(shift_rule_histories.end_time), MAX(shifts.end_time)) as shift_end_time'),
                DB::raw('COALESCE(MIN(shift_rule_histories.workday_min_1), MIN(shifts.workday_min_1)) as shift_workday_min_1'),
                DB::raw('COALESCE(MIN(shift_rule_histories.workday_min_2), MIN(shifts.workday_min_2)) as shift_workday_min_2'),
                // Thêm thông tin từ shift_rule_histories
                DB::raw('MIN(shift_rule_histories.break_times) as shift_break_times'),
                DB::raw('MIN(shift_rule_histories.other_rule_json) as shift_other_rules'),
                DB::raw('MIN(shift_rule_histories.effective_from) as rule_effective_from'),
                DB::raw('MIN(shift_rule_histories.effective_to) as rule_effective_to'),
            ])
            ->join('users', 'users.id', '=', 'timesheets.user_id')
            ->leftJoin('staff_departments', 'staff_departments.id', '=', 'users.staff_department_id')
            ->leftJoin('shifts', 'shifts.id', '=', 'timesheets.shift_id')
            ->leftJoin('shift_rule_histories', 'shift_rule_histories.id', '=', 'timesheets.shift_rule_history_id')
            ->orderBy('user_department_id')
            ->orderBy('user_department_chief_flag')
            ->orderBy('user_id')
            ->orderBy('date')
            ->groupBy([
                'timesheets.user_id',
                'timesheets.date',
                'users.staff_department_id',
                'users.name',
                'users.code',
                'users.staff_department_chief_flag',
                'staff_departments.name',
            ]);
    }

    protected function queryListBase(array $conditions = []): Builder
    {
        return $this->model
            ->newQuery()
            ->when(isset($conditions['codes']), function ($builder) use ($conditions) {
                $builder->whereIn('timesheets.user_id', $conditions['codes']);
            })
            ->when(isset($conditions['user_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('timesheets.user_id', $conditions['user_ids']);
            })
            ->when(isset($conditions['staff_department_ids']), function ($builder) use ($conditions) {
                $builder->whereHas('user', function ($query) use ($conditions) {
                    $query->whereIn('users.staff_department_id', $conditions['staff_department_ids']);
                });
            })
            ->when(isset($conditions['company_address_ids']), function ($builder) use ($conditions) {
                $builder->where(function ($builder) use ($conditions) {
                    $builder->orWhereIn('timesheets.company_address_id', $conditions['company_address_ids']);
                    $builder->orWhereIn('timesheets.checkout_company_address_id', $conditions['company_address_ids']);
                });
            })
            ->when(isset($conditions['from_date']), function ($builder) use ($conditions) {
                $builder->whereDate('timesheets.date', '>=', $conditions['from_date']);
            })
            ->when(isset($conditions['to_date']), function ($builder) use ($conditions) {
                $builder->whereDate('timesheets.date', '<=', $conditions['to_date']);
            });
    }

    public function existsTimesheetByDateAndShiftId(int $userId, string $date, int $shiftId)
    {
        return $this->model
            ->newQuery()
            ->where('user_id', $userId)
            ->where('date', $date)
            ->where('shift_id', $shiftId)
            ->exists();
    }

    public function existsNotCheckoutTimesheetByUserIdAndDate($userId, string $date): bool
    {
        return $this->model
            ->newQuery()
            ->where('date', $date)
            ->where('user_id', $userId)
            ->whereNull('checkout')
            ->exists();
    }

    public function findNotCheckoutTimesheetByUserIdAndDate($userId, string $date): ?Timesheet
    {
        return $this->model
            ->newQuery()
            ->where('date', $date)
            ->where('user_id', $userId)
            ->whereNull('checkout')
            ->first();
    }

    public function getList(array $conditions = [])
    {
        return $this->model
            ->newQuery()
            ->with([
                'shift',
                'companyAddress',
            ])
            ->when(!empty($userId = $conditions['user_id']), function ($builder) use ($userId) {
                $builder->where('user_id', $userId);
            })
            ->when(!empty($date = $conditions['date']), function ($builder) use ($date) {
                $builder->where('date', $date);
            })
            ->orderBy('id')
            ->get();
    }

    public function getListInMonth(array $conditions = [])
    {
        \Log::info($conditions);

        return $this->model
            ->newQuery()
            ->with([
                'shift',
                'companyAddress',
            ])
            ->when(!empty($userId = $conditions['user_id']), function ($builder) use ($userId) {
                $builder->where('user_id', $userId);
            })
            ->when(!empty($date = $conditions['date']), function ($builder) use ($date) {
                $from = Carbon::parse($date)->startOfMonth();
                $to = Carbon::parse($date)->endOfMonth();

                $builder->where('date', '>=', $from)
                    ->where('date', '<=', $to);
            })
            ->orderBy('id')
            ->get();
    }
}
