<?php

namespace App\Repositories\Eloquent;

use App\Contracts\Repositories\LeadReportRepositoryInterface;
use App\Models\LeadReport;
use Illuminate\Contracts\Pagination\Paginator as PaginatorInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class LeadReportRepository extends BaseRepository implements LeadReportRepositoryInterface
{
    public function model(): string
    {
        return LeadReport::class;
    }

    public function getPaginator(int $perPage, array $conditions = []): PaginatorInterface
    {
        $query = $this->model
            ->newQuery()
            ->when(isset($conditions['start_date_at']), function ($builder) use ($conditions) {
                $builder->where('report_date_at', '>=', $conditions['start_date_at']);
            })
            ->when(isset($conditions['end_date_at']), function ($builder) use ($conditions) {
                $builder->where('report_date_at', '<=', $conditions['end_date_at']);
            })
            ->when(isset($conditions['shop_id']), function ($builder) use ($conditions) {
                $builder->whereIn('shop_id', (array) $conditions['shop_id']);
            })
            ->orderBy('id', 'desc');

        // Debug: Log the SQL query
        \Log::info('LeadReport Query SQL: ' . $query->toSql());
        \Log::info('LeadReport Query Bindings: ', $query->getBindings());
        \Log::info('LeadReport Conditions: ', $conditions);

        return $query->paginate($perPage);
    }

    protected function getLeadReportSearchBaseQuery(array $conditions)
    {
        $dateRanges = null;

        if (isset($conditions['date_ranges'])) {
            $dateRanges = $conditions['date_ranges'];
        } elseif (isset($conditions['start_date_at']) && isset($conditions['end_date_at'])) {
            $dateRanges[] = [
                'start_time' => $conditions['start_date_at'],
                'end_time' => $conditions['end_date_at'],
            ];
        }

        return $this->model
            ->newQuery()
            ->limitMktTeam()
            ->when(isset($dateRanges), function ($builder) use ($dateRanges) {
                $builder->where(function ($builder) use ($dateRanges) {
                    foreach ($dateRanges as $range) {
                        $builder->orWhereBetween('report_date_at', [$range['start_time'], $range['end_time']]);
                    }
                });
            })
            ->when(isset($conditions['shop_id']), function ($builder) use ($conditions) {
                $builder->whereIn('shop_id', (array)$conditions['shop_id']);
            })
            ->when(isset($conditions['shop_region']), function ($builder) use ($conditions) {
                $builder
                    ->join('shops', 'shops.id', '=', 'lead_reports.shop_id')
                    ->where('shops.region', $conditions['shop_region']);
            })
            ->when(isset($conditions['area']), function ($builder) use ($conditions) {
                $builder->where('area', $conditions['area']);
            })
            ->when(isset($conditions['marketing_team_id']), function ($builder) use ($conditions) {
                $builder->whereIn('marketing_team_id', (array)$conditions['marketing_team_id']);
            })
            ->when(isset($conditions['mkt_service']), function ($builder) use ($conditions) {
                $builder->whereHas('marketingTeam', function ($builder) use ($conditions) {
                    $builder->whereIn('service', (array)$conditions['mkt_service']);
                });
            })
            ->when(isset($conditions['business_department_id']), function ($builder) use ($conditions) {
                $builder->whereIn('business_department_id', (array)$conditions['business_department_id']);
            });
    }

    public function existsByReportDateAt(string $reportDateAt, $shopId, $marketingTeamId, int $area, $doctorId): bool
    {
        return $this->model
            ->newQuery()
            ->where('report_date_at', $reportDateAt)
            ->where('shop_id', $shopId)
            ->where('marketing_team_id', $marketingTeamId)
            ->where('area', $area)
            ->when(isset($doctorId), function ($builder) use ($doctorId) {
                $builder->where('doctor_id', $doctorId);
            })
            ->exists();
    }

    public function updateFieldsByConditions(array $conditions, array $fieldsUpdate)
    {
        return $this->model
            ->newQuery()
            ->where('shop_id', $conditions['shop_id'])
            ->where('marketing_team_id', $conditions['marketing_team_id'])
            ->where('doctor_id', $conditions['doctor_mkt_id'])
            ->where('report_date_at', $conditions['booking_date_at'])
            ->update($fieldsUpdate);
    }

    public function updateById($id, array $fields)
    {
        return $this->model
            ->newQuery()
            ->where('id', $id)
            ->update($fields);
    }

    public function queryLeadReportStatisticAllType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields))
            ->groupBy($groupByFields)
            ->orderBy('report_date_at');
    }

    public function queryLeadReportStatisticSingleDepartmentType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields))
            ->when(isset($conditions['department_ids']), function ($builder) use ($conditions) {
                $builder->whereHas('marketingTeam', function ($builder) use ($conditions) {
                    $builder->whereIn('department_id', $conditions['department_ids']);
                });
            })
            ->when(isset($conditions['marketing_team_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('marketing_team_id', (array)$conditions['marketing_team_ids']);
            })
            ->when(isset($conditions['doctor_id']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_id']);
            })
            ->when(isset($conditions['doctor_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_ids']);
            })
            ->groupBy($groupByFields)
            ->orderBy('report_date_at');
    }

    public function queryLeadReportStatisticSingleProductType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields))
            ->when(isset($conditions['product_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('product_id', $conditions['product_ids']);
            })
            ->groupBy($groupByFields)
            ->orderBy('report_date_at');
    }

    public function queryLeadReportStatisticDepartmentMultiMarketingTeamsType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
            'marketing_team_id',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields))
            ->when(isset($conditions['department_ids']), function ($builder) use ($conditions) {
                $builder->whereHas('marketingTeam', function ($builder) use ($conditions) {
                    $builder->whereIn('department_id', $conditions['department_ids']);
                });
            })
            ->when(isset($conditions['marketing_team_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('marketing_team_id', (array)$conditions['marketing_team_ids']);
            })
            ->when(isset($conditions['doctor_id']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_id']);
            })
            ->when(isset($conditions['doctor_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_ids']);
            })
            ->groupBy($groupByFields)
            ->orderBy('report_date_at');
    }

    public function queryLeadReportStatisticProductMultiType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
            'product_id',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields, [
                'products.name as product_name',
            ]))
            ->join('products', 'products.id', '=', 'lead_reports.product_id')
            ->when(isset($conditions['product_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('product_id', $conditions['product_ids']);
            })
            ->groupBy(array_merge($groupByFields, [
                'products.name',
            ]))
            ->orderBy('report_date_at');
    }

    public function queryLeadReportStatisticMultipleDoctorsType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();

        $groupByFields = [
            'lead_reports.doctor_id',
            'lead_reports.department_fee_ratio',
            'lead_reports.account_fee_ratio',
            'doctors.name',
            'lead_reports.report_date_at',
            'marketing_teams.department_id',
            'marketing_team_id',
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, [
                'doctors.name as doctor_name',
                'marketing_teams.department_id as marketing_team_department_id',
                'lead_reports.report_date_at as lead_report_report_date_at',
                'marketing_team_id',
            ], $groupByFields))
            ->join('doctors', 'doctors.id', '=', 'lead_reports.doctor_id')
            ->join('marketing_teams', 'marketing_teams.id', '=', 'lead_reports.marketing_team_id')
            ->when(isset($conditions['department_ids']), function ($builder) use ($conditions) {
                $builder->whereHas('marketingTeam', function ($builder) use ($conditions) {
                    $builder->whereIn('department_id', $conditions['department_ids']);
                });
            })
            ->when(isset($conditions['marketing_team_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('marketing_team_id', (array)$conditions['marketing_team_ids']);
            })
            ->when(isset($conditions['doctor_id']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_id']);
            })
            ->when(isset($conditions['doctor_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('doctor_id', (array)$conditions['doctor_ids']);
            })
            ->groupBy($groupByFields)
            ->orderBy('lead_reports.doctor_id');
    }

    public function queryLeadReportStatisticSingleDoctorType(array $conditions = []): Builder
    {
        $selectSumFields = collect(LeadReport::SHOULD_CALC_SUM_FIELDS)->map(function ($field) {
            return DB::raw("SUM({$field}) as {$field}");
        })->toArray();
        $groupByFields = [
            'report_date_at',
            'department_fee_ratio',
            'account_fee_ratio',
            'marketing_team_id'
        ];

        return $this->getLeadReportSearchBaseQuery($conditions)
            ->select(array_merge($selectSumFields, $groupByFields))
            ->when(isset($conditions['doctor_id']), function ($builder) use ($conditions) {
                $builder->where('doctor_id', $conditions['doctor_id']);
            })
            ->when(isset($conditions['marketing_team_ids']), function ($builder) use ($conditions) {
                $builder->whereIn('marketing_team_id', (array)$conditions['marketing_team_ids']);
            })
            ->groupBy($groupByFields)
            ->orderBy('report_date_at');
    }
}
