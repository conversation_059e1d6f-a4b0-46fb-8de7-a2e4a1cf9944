<?php

namespace App\Services\LeadReport;

use App\Contracts\Repositories\BookingRepositoryInterface;
use App\Contracts\Repositories\CustomerRevenueRepositoryInterface;
use App\Contracts\Repositories\LeadReportRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamDoctorRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamRepositoryInterface;
use App\Enums\LeadReport\AreaEnum;
use App\Enums\Permission\GroupEnum;
use App\Models\LeadReport;
use App\Models\User;
use App\Services\BaseService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class LeadReportService extends BaseService
{
    protected LeadReportRepositoryInterface $leadReportRepository;
    protected MarketingTeamDoctorRepositoryInterface $marketingTeamDoctorRepository;
    protected MarketingTeamRepositoryInterface $marketingTeamRepository;
    protected BookingRepositoryInterface $bookingRepository;
    protected CustomerRevenueRepositoryInterface $customerRevenueRepository;

    public function __construct(
        LeadReportRepositoryInterface          $leadReportRepository,
        MarketingTeamDoctorRepositoryInterface $marketingTeamDoctorRepository,
        MarketingTeamRepositoryInterface       $marketingTeamRepository,
        BookingRepositoryInterface             $bookingRepository,
        CustomerRevenueRepositoryInterface     $customerRevenueRepository
    ) {
        $this->leadReportRepository = $leadReportRepository;
        $this->marketingTeamDoctorRepository = $marketingTeamDoctorRepository;
        $this->marketingTeamRepository = $marketingTeamRepository;
        $this->bookingRepository = $bookingRepository;
        $this->customerRevenueRepository = $customerRevenueRepository;
    }

    public function getPaginator(int $perPage, User $user, array $conditions = []): Paginator
    {
        if (
            $user->hasPermissionTo(permission_name('viewAndCreateAndUpdateLimitByShop', GroupEnum::LeadReport))
            && empty($conditions['shop_id'])
        ) {
            $conditions['shop_id'] = $user->shops->pluck('id')->toArray();
        }

        // Debug: Log the final conditions being passed to repository
        \Log::info('LeadReportService final conditions: ', $conditions);

        return $this->leadReportRepository->getPaginator($perPage, $conditions);
    }

    public function store(User $creator, array $attributes): LeadReport
    {
        $attributes['report_user_id'] = $creator->getKey();

        return DB::transaction(function () use ($attributes) {
            $leadReport = new LeadReport($attributes);

            // Get department ID from marketing team
            $departmentId = $leadReport->marketingTeam?->department_id;

            // Determine which account fee ratio to use based on department and report date
            $attributes['account_fee_ratio'] = $this->getAccountFeeRatioByDepartment(
                $departmentId,
                isset($attributes['report_date_at']) ? Carbon::parse($attributes['report_date_at']) : Carbon::now()
            );

            $leadReport->fill($attributes);
            $leadReport->fill([
                'department_fee_ratio' => $leadReport->marketingTeam?->department?->fee_ratio ?? 1,
            ]);
            $leadReport->businessDepartment()->associate($leadReport->marketingTeam?->business_department_id);
            $this->leadReportRepository->save($leadReport);

            $this->syncMarketingTeamWithDoctor($attributes['marketing_team_id'], $attributes['doctor_id']);

            return $leadReport;
        });
    }

    public function update(LeadReport $leadReport, array $attributes): LeadReport
    {
        return DB::transaction(function () use ($leadReport, $attributes) {
            $oldMktTeamId = $leadReport->marketing_team_id;
            $oldDoctorId = $leadReport->doctor_id;

            $leadReport->fill($attributes);

            // Update account fee ratio if marketing team or report date changed
            if (isset($attributes['marketing_team_id']) || isset($attributes['report_date_at'])) {
                $departmentId = $leadReport->marketingTeam?->department_id;
                $reportDate = isset($attributes['report_date_at']) ?
                    Carbon::parse($attributes['report_date_at']) :
                    $leadReport->report_date_at;

                $leadReport->account_fee_ratio = $this->getAccountFeeRatioByDepartment($departmentId, $reportDate);
            }

            $leadReport->fill([
                'department_fee_ratio' => $leadReport->marketingTeam?->department?->fee_ratio ?? 1,
            ]);
            $leadReport->businessDepartment()->associate($leadReport->marketingTeam?->business_department_id);
            $this->leadReportRepository->save($leadReport);

            $this->syncMarketingTeamWithDoctor($oldMktTeamId, $oldDoctorId);
            if (isset($attributes['marketing_team_id'])) {
                $this->syncMarketingTeamWithDoctor($attributes['marketing_team_id'], $attributes['doctor_id']);
            }

            return $leadReport;
        });
    }

    public function syncMarketingTeamWithDoctor($mktTeamId, $doctorId)
    {
        $marketingTeam = $this->marketingTeamRepository->find($mktTeamId);
        $isExistDoctor = $this->marketingTeamDoctorRepository->checkDoctorExist($mktTeamId, $doctorId);
        $isExistLeadReport = $this->leadReportRepository->existsByCondition([
            'marketing_team_id' => $mktTeamId,
            'doctor_id' => $doctorId,
        ]);

        if ($isExistDoctor && !$isExistLeadReport) {
            return $marketingTeam->doctors()->detach($doctorId);
        }

        if (!$isExistDoctor && $isExistLeadReport) {
            return $marketingTeam->doctors()->attach($doctorId);
        }

        return false;
    }

    public function updateLeadReport(array $conditions)
    {
        $fieldsUpdate = $this->calCustomerCount($conditions);

        $this->leadReportRepository->updateFieldsByConditions($conditions, $fieldsUpdate);
    }

    public function updateCustomerRevenueForLeadReport(array $conditions)
    {
        $fieldsUpdate = $this->calCustomerRevenue($conditions);

        $this->leadReportRepository->updateFieldsByConditions($conditions, $fieldsUpdate);
    }

    public function calCustomerCount(array $conditions)
    {
        return [
            'new_customer_count' => $this->bookingRepository->getNewCustomerCountByConditions($conditions),
            'random_customer_count' => $this->bookingRepository->getRandomCustomerCountByConditions($conditions),
            'failed_customer_count' => $this->bookingRepository->getFailedCustomerCountByConditions($conditions),
            'paid_deposit_customer_count' => $this->bookingRepository->getPaidDepositCustomerCountByConditions($conditions),
            'new_customer_revenue' => $this->customerRevenueRepository->getNewCustomerRevenueByConditions($conditions),
            'old_customer_revenue' => $this->customerRevenueRepository->getOldCustomerRevenueByConditions($conditions),
            'customer_count_rejected_by_shop' => $this->bookingRepository->getCustomerCountRejectedByShopByConditions($conditions),
        ];
    }

    public function calCustomerRevenue(array $conditions)
    {
        return [
            'new_customer_revenue' => $this->customerRevenueRepository->getNewCustomerRevenueByConditions($conditions),
            'old_customer_revenue' => $this->customerRevenueRepository->getOldCustomerRevenueByConditions($conditions),
            'other_customer_revenue' => $this->customerRevenueRepository->getOtherCustomerRevenueByConditions($conditions),
        ];
    }

    // Fields = (unique = shop_id + marketing_team_id + doctor_id + report_date_at + area (auto = 1 (Inside)))
    public function createLeadReportWhenFieldsNotExist($conditions)
    {
        if ($conditions['shop_id']
            && $conditions['marketing_team_id']
            && $conditions['doctor_mkt_id']
            && $conditions['booking_date_at']
        ) {
            $conditions = [
                'shop_id' => $conditions['shop_id'],
                'marketing_team_id' => $conditions['marketing_team_id'],
                'doctor_id' => $conditions['doctor_mkt_id'],
                'report_date_at' => $conditions['booking_date_at'],
                'area' => AreaEnum::Inside,
            ];
            $existingLeadReport = $this->leadReportRepository->existsByCondition($conditions);

            if (!$existingLeadReport) {
                $this->store(staff(), $conditions);
            }
        }
    }

    public function delete(LeadReport $leadReport)
    {
        $leadReport->delete();
    }

    /**
     * Get account fee ratio based on department and report date
     */
    private function getAccountFeeRatioByDepartment(?int $departmentId, Carbon $reportDate): float
    {
        if ($departmentId === 6) {
            // For department ID 6
            $cutoffDate = Carbon::parse(config('common.account_fee_ratio_after_date_dept_6'));
            if ($reportDate->greaterThanOrEqualTo($cutoffDate)) {
                // From May 2025: 1%
                return config('common.account_fee_ratio_dept6_new');
            } else {
                // Before May 2025: 3%
                return config('common.account_fee_ratio_dept6_old');
            }
        } else {
            // For other departments
            $cutoffDate = Carbon::parse(config('common.account_fee_ratio_after_date_others'));
            if ($reportDate->greaterThanOrEqualTo($cutoffDate)) {
                // From January 2025: 3%
                return config('common.account_fee_ratio_others_new');
            } else {
                // Before January 2025: 2%
                return config('common.account_fee_ratio_others_old');
            }
        }
    }
}
