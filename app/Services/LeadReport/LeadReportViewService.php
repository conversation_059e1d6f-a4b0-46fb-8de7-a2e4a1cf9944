<?php

namespace App\Services\LeadReport;

use App\Contracts\Repositories\DoctorRepositoryInterface;
use App\Contracts\Repositories\MarketingTeamRepositoryInterface;
use App\Contracts\Repositories\ProductRepositoryInterface;
use App\Contracts\Repositories\ShopRepositoryInterface;
use App\Enums\LeadReport\AreaEnum;
use App\Enums\Permission\GroupEnum;
use App\Models\LeadReport;
use App\Models\User;
use App\Services\BaseService;
use App\Services\Traits\ManagesDoctorToLeadReportForUser;
use Illuminate\Http\Request;

class LeadReportViewService extends BaseService
{
    use ManagesDoctorToLeadReportForUser;

    protected LeadReportService $leadReportService;
    protected ProductRepositoryInterface $productRepository;
    protected MarketingTeamRepositoryInterface $marketingTeamRepository;
    protected ShopRepositoryInterface $shopRepository;
    protected DoctorRepositoryInterface $doctorRepository;

    public function __construct(
        LeadReportService                $leadReportService,
        ProductRepositoryInterface       $productRepository,
        MarketingTeamRepositoryInterface $marketingTeamRepository,
        ShopRepositoryInterface          $shopRepository,
        DoctorRepositoryInterface        $doctorRepository,
    ) {
        $this->leadReportService = $leadReportService;
        $this->productRepository = $productRepository;
        $this->marketingTeamRepository = $marketingTeamRepository;
        $this->shopRepository = $shopRepository;
        $this->doctorRepository = $doctorRepository;
    }

    public function index(array $filters, User $viewer): array
    {
        $viewFilters = $this->getFiltersData(request(), staff());

        // Debug: Log the filters being passed
        \Log::info('LeadReportViewService filters: ', $filters);
        \Log::info('LeadReportViewService viewFilters: ', $viewFilters);

        $leadReports = $this->leadReportService
            ->getPaginator(
                config('common.pagination.per_page.lead_report'),
                $viewer,
                $filters
            );
        $leadReports->appends($viewFilters['params']);

        return [
            'leadReports' => $leadReports,
            'filters' => $viewFilters,
        ];
    }

    public function createOrEdit(User $creatorOrEditor, LeadReport $leadReport = null): array
    {
        $isEdit = (bool)$leadReport;

        if (!$leadReport) {
            $leadReport = new LeadReport();

            $leadReport->report_user_id = $creatorOrEditor->getKey();
            $leadReport->report_date_at = now();
        }

        if ($creatorOrEditor->hasPermissionTo(permission_name('viewAndCreateAndUpdateLimitByShop', GroupEnum::LeadReport))) {
            $shops = $creatorOrEditor->shops;
        } else {
            $shops = $this->shopRepository->all();
        }

        $marketingTeams = $this->marketingTeamRepository->all();
        $products = $this->productRepository->allMainProducts();
        $areas = AreaEnum::getDescriptions();

        $reportDateAt = $leadReport->report_date_at
            ? $leadReport->report_date_at->format(config('common.datetime.format.client.date'))
            : null;

        $doctors = $this->getAvailableDoctorsToLeadReportByUser($creatorOrEditor);

        $enableFields = array_flip($this->getEnableFieldsByUser($creatorOrEditor));

        $hiddenBtnEdit = false;
        if ($isEdit) {
            $hiddenBtnEdit = should_hidden_btn_edit($leadReport->report_date_at);
        }

        return compact(
            'isEdit',
            'leadReport',
            'shops',
            'doctors',
            'marketingTeams',
            'products',
            'areas',
            'reportDateAt',
            'enableFields',
            'hiddenBtnEdit',
        );
    }

    protected function getFiltersData(Request $request, User $user): array
    {
        if ($user->hasPermissionTo(permission_name('viewAndCreateAndUpdateLimitByShop', GroupEnum::LeadReport))) {
            $shops = $user->shops;
        } else {
            $shops = $this->shopRepository->all();
        }

        return [
            'data' => [
                'shops' => $shops,
            ],
            'params' => [
                'shop_id' => $request->query('shop_id'),
                'start_date_at' => $request->query('start_date_at'),
                'end_date_at' => $request->query('end_date_at'),
            ],
        ];
    }

    public function getEnableFieldsByUser(User $user): array
    {
        /*if ($user->isSupervisor()) {
            return [
                'shop_id',
                'marketing_team_id',
                'product_id',
                'report_date_at',
                'area',
                'new_customer_count',
                'random_customer_count',
                'failed_customer_count',
                'group_fee',
                'content_department_fee',
                'budget',
                'inbox_count',
                'phone_number_count',
                'date_booking_count',
                'month_booking_count',
            ];
        }*/

        if ($user->isBackofficeAccountant()) {
            return [
                'new_customer_revenue',
                'old_customer_revenue',
                'other_customer_revenue',
            ];
        }

        return [
            'shop_id',
            'marketing_team_id',
            'doctor_id',
            'report_date_at',
            'area',
            'new_customer_count',
            'random_customer_count',
            'failed_customer_count',
            'paid_deposit_customer_count',
            'customer_count_rejected_by_shop',
            'new_customer_revenue',
            'old_customer_revenue',
            'other_customer_revenue',
            'group_fee',
            'content_department_fee',
            'budget',
            'account_fee_ratio',
            'inbox_count',
            'phone_number_count',
            'province_phone_number_count',
            'contacted_phone_number_count',
            'date_booking_count',
            'month_booking_count',
            'effective_point',
            'serve_point'
        ];
    }
}
