<?php

namespace App\Services\Timesheet;

use App\Contracts\Repositories\CompanyAddressRepositoryInterface;
use App\Contracts\Repositories\ShiftRepositoryInterface;
use App\Contracts\Repositories\TimesheetRepositoryInterface;
use App\Contracts\Repositories\UserRepositoryInterface;
use App\Exports\Timesheet\TimesheetMasterExport;
use App\Models\Timesheet;
use App\Models\User;
use App\Services\BaseService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class TimesheetService extends BaseService
{
    protected TimesheetRepositoryInterface $timesheetRepository;
    protected ShiftRepositoryInterface $shiftRepository;
    protected CompanyAddressRepositoryInterface $companyAddressRepository;
    protected UserRepositoryInterface $userRepository;

    public function __construct(
        TimesheetRepositoryInterface      $timesheetRepository,
        ShiftRepositoryInterface          $shiftRepository,
        CompanyAddressRepositoryInterface $companyAddressRepository,
        UserRepositoryInterface           $userRepository
    ) {
        $this->timesheetRepository = $timesheetRepository;
        $this->shiftRepository = $shiftRepository;
        $this->companyAddressRepository = $companyAddressRepository;
        $this->userRepository = $userRepository;
    }

    public function store(User $user, array $attributes)
    {
        $timesheet = new Timesheet($attributes);

        // Lưu quy tắc ca làm việc hiện tại vào timesheet
        if (!empty($timesheet->shift_id) && empty($timesheet->shift_rule_history_id)) {
            $shift = $this->shiftRepository->findById($timesheet->shift_id);
            $shiftRule = $shift->getActiveRule($timesheet->date);
            if ($shiftRule) {
                $timesheet->shift_rule_history_id = $shiftRule->id;
            }
        }

        $this->timesheetRepository->save($timesheet);

        return $timesheet;
    }

    public function update(Timesheet $timesheet, array $attributes)
    {
        $timesheet->fill($attributes);

        // Nếu thay đổi ca làm việc hoặc ngày, cập nhật lại quy tắc ca làm việc
        $shiftChanged = isset($attributes['shift_id']) && $attributes['shift_id'] != $timesheet->getOriginal('shift_id');
        $dateChanged = isset($attributes['date']) && $attributes['date'] != $timesheet->getOriginal('date');

        if (($shiftChanged || $dateChanged) && empty($attributes['shift_rule_history_id'])) {
            $shift = $this->shiftRepository->findById($timesheet->shift_id);
            $shiftRule = $shift->getActiveRule($timesheet->date);
            if ($shiftRule) {
                $timesheet->shift_rule_history_id = $shiftRule->id;
            }
        }

        $this->timesheetRepository->save($timesheet);

        return $timesheet;
    }

    public function getPaginator(int $perPage, array $filters = []): Paginator
    {
        return $this->timesheetRepository->getPaginator($perPage, $filters);
    }

    public function delete(Timesheet $timesheet)
    {
        $timesheet->delete();
    }

    public function checkin(User $user, Carbon $time, array $options = []): Timesheet
    {
        if (!$this->isCheckin($user, $time)) {
            abort(403);
        }

        $timesheet = new Timesheet([
            'date' => $time,
            'checkin' => $time,
        ]);

        $timesheet->user()->associate($user);

        $shift = $this->shiftRepository->findById($options['shift_id']);
        $timesheet->shift()->associate($shift);

        // Lưu quy tắc ca làm việc hiện tại vào timesheet
        $shiftRule = $shift->getActiveRule($time);
        if ($shiftRule) {
            $timesheet->shift_rule_history_id = $shiftRule->id;
        }

        if (!empty($companyAddressId = $options['company_address_id'])) {
            $companyAddress = $this->companyAddressRepository->findById($companyAddressId);
            $timesheet->companyAddress()->associate($companyAddress);
        }

        $user->defaultShift()->associate($shift);

        return DB::transaction(function () use ($timesheet, $user) {
            $this->timesheetRepository->save($timesheet);
            $this->userRepository->save($user);

            return $timesheet;
        });
    }

    public function checkout(User $user, Carbon $time, array $options = []): Timesheet
    {
        if (!$this->isCheckout($user, $time)) {
            abort(403);
        }

        $timesheet = $this->timesheetRepository
            ->findNotCheckoutTimesheetByUserIdAndDate(
                $user->getKey(),
                $time->format(config('common.datetime.format.database.date'))
            );

        $timesheet->fill([
            'checkout' => $time,
        ]);

        if (!empty($checkoutCompanyAddressId = $options['checkout_company_address_id'])) {
            $checkoutCompanyAddress = $this->companyAddressRepository->findById($checkoutCompanyAddressId);
            $timesheet->checkoutCompanyAddress()->associate($checkoutCompanyAddress);
        }

        $this->timesheetRepository->save($timesheet);

        return $timesheet;
    }

    public function getCheckingStatus(User $user, Carbon $time): string
    {
        if ($this->isCheckout($user, $time)) {
            return 'checkout';
        }

        return 'checkin';
    }

    public function isCheckout(User $user, Carbon $time): bool
    {
        return $this->timesheetRepository
            ->existsNotCheckoutTimesheetByUserIdAndDate(
                $user->getKey(),
                $time->format(config('common.datetime.format.database.date'))
            );
    }

    public function isCheckin(User $user, Carbon $time): bool
    {
        return !$this->isCheckout($user, $time);
    }

    public function export(array $conditions)
    {
        $currentTime = now();

        $nowFormat = $currentTime->format(config('common.datetime.format.client.date_download'));
        $fileName = $nowFormat . '_' . config('export.staff_timesheets.excel_name')
            . '.' . config('export.staff_timesheets.extension_detector');


        $export = new TimesheetMasterExport(
            $this->timesheetRepository,
            $this->shiftRepository,
            $conditions
        );

        return $export->download($fileName);
    }

    public function exportSummary(array $conditions)
    {
        $currentTime = now();

        $nowFormat = $currentTime->format(config('common.datetime.format.client.date_download'));
        $fileName = $nowFormat . '_bao_cao_tong_hop_cong.xlsx';

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\Timesheet\TimesheetSummaryExport($conditions),
            $fileName
        );
    }

    public function getUserInfo($user): User
    {
        $user->shift_ids = $user->shifts->pluck('id')->toArray();

        return $user;
    }

    public function getListByUser(User $user, array $conditions = [])
    {
        $conditions['user_id'] = $user->getKey();

        return $this->timesheetRepository->getList($conditions);
    }

    public function getListInMonth(User $user, array $conditions = [])
    {
        $conditions['user_id'] = $user->getKey();

        return $this->timesheetRepository->getListInMonth($conditions);
    }
}
