<?php

namespace App\Models;

use App\Contracts\Services\Timesheet\ShiftLogicTypeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Timesheet extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'date',
        'checkin',
        'checkout',
        'user_id',
        'shift_id',
        'company_address_id',
        'checkout_company_address_id',
        'shift_rule_history_id',
        'workday',
    ];

    protected $casts = [
        'date' => 'date',
        'checkin' => 'datetime',
        'checkout' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function companyAddress()
    {
        return $this->belongsTo(CompanyAddress::class);
    }

    public function checkoutCompanyAddress()
    {
        return $this->belongsTo(CompanyAddress::class, 'checkout_company_address_id');
    }

    public function shiftRuleHistory()
    {
        return $this->belongsTo(ShiftRuleHistory::class);
    }

    /**
     * L<PERSON>y quy tắc ca làm việc áp dụng cho timesheet này
     *
     * @return ShiftRuleHistory
     */
    public function getShiftRule()
    {
        // Nếu đã lưu shift_rule_history_id, ưu tiên sử dụng
        if ($this->shift_rule_history_id) {
            return $this->shiftRuleHistory;
        }

        // Nếu chưa lưu, lấy quy tắc phù hợp với ngày của timesheet
        return $this->shift->getActiveRule($this->date);
    }

    public function calcWorkDayHours(): float
    {
        /** @var ShiftLogicTypeInterface $shiftLogicService */
        $shiftLogicService = $this->shift->getLogicTypeService();
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc
            return $shiftLogicService->calcWorkdayHours([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'break_times' => [
                    [
                        'start' => '12:00:00',
                        'end' => '13:00:00'
                    ]
                ],
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
            ]);
        }

        return $shiftLogicService->calcWorkdayHours([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'break_times' => $shiftRule->break_times,
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
        ]);
    }

    public function calcWorkday(): float
    {
        /** @var ShiftLogicTypeInterface $shiftLogicService */
        $shiftLogicService = $this->shift->getLogicTypeService();
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc
            return $shiftLogicService->calcWorkday([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
            ]);
        }

        return $shiftLogicService->calcWorkday([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
            'break_times' => $shiftRule->break_times,
        ]);
    }

    public function calcWorkDayRealHours(): float
    {
        /** @var ShiftLogicTypeInterface $shiftLogicService */
        $shiftLogicService = $this->shift->getLogicTypeService();
        $shiftRule = $this->getShiftRule();

        if (!$shiftRule) {
            // Fallback nếu không tìm thấy quy tắc
            return $shiftLogicService->calcWorkdayRealHours([
                'schedule_start_time' => $this->shift->start_time,
                'schedule_end_time' => $this->shift->end_time,
                'start_time' => $this->checkin?->format('H:i:s'),
                'end_time' => $this->checkout?->format('H:i:s'),
                'break_times' => [
                    [
                        'start' => '12:00:00',
                        'end' => '13:00:00'
                    ]
                ],
                'workday_min_1' => $this->shift->workday_min_1,
                'workday_min_2' => $this->shift->workday_min_2,
            ]);
        }

        return $shiftLogicService->calcWorkdayRealHours([
            'schedule_start_time' => $shiftRule->start_time,
            'schedule_end_time' => $shiftRule->end_time,
            'start_time' => $this->checkin?->format('H:i:s'),
            'end_time' => $this->checkout?->format('H:i:s'),
            'break_times' => $shiftRule->break_times,
            'workday_min_1' => $shiftRule->workday_min_1,
            'workday_min_2' => $shiftRule->workday_min_2,
        ]);
    }
}
