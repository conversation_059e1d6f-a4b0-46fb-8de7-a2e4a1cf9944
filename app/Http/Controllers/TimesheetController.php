<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\HasExpectsJsonResponse;
use App\Http\Requests\Timesheet\ExportTimesheetRequest;
use App\Http\Requests\Timesheet\ListTimesheetRequest;
use App\Http\Requests\Timesheet\StoreTimesheetRequest;
use App\Http\Requests\Timesheet\UpdateTimesheetRequest;
use App\Models\Timesheet;
use App\Models\User;
use App\Services\Timesheet\TimesheetService;
use App\Services\Timesheet\TimesheetViewService;
use Illuminate\Http\Request;

class TimesheetController extends Controller
{
    use HasExpectsJsonResponse;

    protected $timesheetService;
    protected $timesheetViewService;

    public function __construct(
        TimesheetService $timesheetService,
        TimesheetViewService $timesheetViewService
    ) {
        $this->timesheetService = $timesheetService;
        $this->timesheetViewService = $timesheetViewService;
    }

    public function index(ListTimesheetRequest $request)
    {
        $this->authorize('viewAny', Timesheet::class);

        $data = $this->timesheetViewService->index($request->validated());

        return view(
            'pages.timesheets.index',
            array_merge(['redirectUrl' => url()->full()], $data)
        );
    }

    public function create()
    {
        $this->authorize('create', Timesheet::class);

        return view(
            'pages.timesheets.create-edit',
            $this->timesheetViewService->createOrEdit()
        );
    }

    public function store(StoreTimesheetRequest $request)
    {
        $this->authorize('create', Timesheet::class);
        $this->timesheetService->store($request->user, $request->validated());
        $this->notifySessionSuccess(
            __('messages.create_object_successfully', ['obj' => __('objects.timesheet')])
        );

        return redirect()->route('timesheets.index');
    }

    public function edit(Timesheet $timesheet, Request $request)
    {
        $this->authorize('update', $timesheet);

        return view(
            'pages.timesheets.create-edit',
            array_merge(
                ['redirectUrl' => $request->redirect_url],
                $this->timesheetViewService->createOrEdit($timesheet)
            )
        );
    }

    public function update(UpdateTimesheetRequest $request, Timesheet $timesheet)
    {
        $this->authorize('update', $timesheet);
        $this->timesheetService->update($timesheet, $request->validated());
        $this->notifySessionSuccess(
            __('messages.edit_object_successfully', ['obj' => __('objects.timesheet')])
        );

        if (empty($request->redirect_url)) {
            return redirect()->route('timesheets.index');
        }

        return redirect($request->redirect_url);
    }

    public function destroy(Timesheet $timesheet)
    {
        $this->authorize('delete', $timesheet);

        $this->timesheetService->delete($timesheet);

        $this->notifySessionSuccess(
            __('messages.delete_object_successfully', ['obj' => __('objects.timesheet')])
        );

        return redirect()->back();
    }

    public function getUserInfo(User $user)
    {
        $this->authorize('getUserInfo', Timesheet::class);
        $user = $this->timesheetService->getUserInfo($user);

        return $this->responseJsonSuccess($user);
    }

    public function export(ExportTimesheetRequest $exportTimesheetRequest)
    {
        $this->authorize('export', Timesheet::class);

        // Tạm thời chỉ export user ID 2845
        $conditions = $exportTimesheetRequest->validated();
        $conditions['user_ids'] = [2845];

        return $this->timesheetService->export($conditions);
    }
}
