<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Timesheet\TimesheetAttendanceExportService;
use App\Exports\Timesheet\TimesheetAttendanceExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\StaffDepartment;
use App\Models\User;

class TimesheetAttendanceController extends Controller
{
    protected $exportService;

    public function __construct(TimesheetAttendanceExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Hiển thị trang export bảng chấm công
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Lấy danh sách phòng ban
        $departments = StaffDepartment::orderBy('name')->get();
        
        // Lấy danh sách nhân viên (nếu user có quyền xem)
        $users = collect();
        if ($user->can('viewAny', User::class)) {
            $users = User::with('staffDepartment')
                        ->whereNull('deleted_at')
                        ->orderBy('name')
                        ->get();
        }

        return view('pages.timesheet.attendance-export', compact('departments', 'users'));
    }

    /**
     * Export Excel bảng chấm công
     */
    public function exportExcel(Request $request)
    {
        $user = auth()->user();

        // Validate input
        $request->validate([
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2020|max:2030',
            'department_id' => 'nullable|exists:staff_departments,id',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $filters = [
            'month' => $request->input('month'),
            'year' => $request->input('year'),
            'department_id' => $request->input('department_id'),
            'user_ids' => $request->input('user_ids')
        ];

        // Kiểm tra quyền truy cập dữ liệu
        if (!$user->can('exportAttendance', \App\Models\Timesheet::class)) {
            abort(403, 'Bạn không có quyền xuất bảng chấm công chi tiết');
        }

        try {
            // Lấy dữ liệu chấm công
            $timesheets = $this->exportService->getAttendanceData($filters);

            // Thông tin bổ sung cho export
            $departmentName = null;
            if ($filters['department_id']) {
                $department = StaffDepartment::find($filters['department_id']);
                $departmentName = $department ? $department->name : null;
            }

            $data = [
                'timesheets' => $timesheets,
                'month' => $filters['month'],
                'year' => $filters['year'],
                'department' => $departmentName,
                'isExport' => true
            ];

            // Tạo tên file
            $nowFormat = now()->format(config('common.datetime.format.client.date_download'));
            $excelName = config('export.timesheet_attendance.excel_name');
            $extension = config('export.timesheet_attendance.extension_detector');
            $monthYear = sprintf('%02d_%d', $filters['month'], $filters['year']);
            $fileName = "{$nowFormat}_{$excelName}_{$monthYear}.{$extension}";

            return Excel::download(new TimesheetAttendanceExport($data), $fileName);

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi xuất file: ' . $e->getMessage());
        }
    }

    /**
     * API để lấy danh sách nhân viên theo phòng ban
     */
    public function getUsersByDepartment(Request $request)
    {
        $departmentId = $request->input('department_id');
        
        if (!$departmentId) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng chọn phòng ban'
            ]);
        }

        $users = User::where('staff_department_id', $departmentId)
                    ->whereNull('deleted_at')
                    ->select('id', 'name', 'user_code')
                    ->orderBy('name')
                    ->get();

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Preview dữ liệu trước khi export
     */
    public function preview(Request $request)
    {
        $user = auth()->user();

        // Validate input
        $request->validate([
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2020|max:2030',
            'department_id' => 'nullable|exists:staff_departments,id',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        if (!$user->can('exportAttendance', \App\Models\Timesheet::class)) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xem dữ liệu chấm công chi tiết'
            ], 403);
        }

        $filters = [
            'month' => $request->input('month'),
            'year' => $request->input('year'),
            'department_id' => $request->input('department_id'),
            'user_ids' => $request->input('user_ids')
        ];

        try {
            // Lấy dữ liệu mẫu (giới hạn 10 bản ghi)
            $timesheets = $this->exportService->getAttendanceData($filters)->take(10);

            $stats = [
                'total_records' => $this->exportService->getAttendanceData($filters)->count(),
                'preview_records' => $timesheets->count(),
                'unique_users' => $timesheets->groupBy('user_id')->count(),
                'total_workdays' => $timesheets->sum('final_workday'),
                'total_overtime' => $timesheets->sum('overtime_hours'),
                'total_violations' => $timesheets->sum('violation_count')
            ];

            return response()->json([
                'success' => true,
                'data' => $timesheets,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
