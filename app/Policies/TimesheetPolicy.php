<?php

namespace App\Policies;

use App\Enums\Permission\GroupEnum;
use App\Models\User;
use App\Services\Timesheet\TimesheetService;

class TimesheetPolicy
{
    protected TimesheetService $timesheetService;

    public function __construct(TimesheetService $timesheetService)
    {
        $this->timesheetService = $timesheetService;
    }

    public function before(User $user, string $ability): ?bool
    {
        if (in_array($ability, ['checkin', 'checkout', 'getUserInfo'])) {
            return null;
        }

        if ($user->cannot(permission_name($ability, GroupEnum::Timesheet))) {
            return false;
        }

        return null;
    }

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user): bool
    {
        return true;
    }

    public function delete(User $user): bool
    {
        return true;
    }

    public function export(User $user): bool
    {
        return true;
    }

    public function exportAttendance(User $user): bool
    {
        return true;
    }

    public function checkin(User $user): bool
    {
        return $this->timesheetService->isCheckin($user, now());
    }

    public function checkout(User $user): bool
    {
        return $this->timesheetService->isCheckout($user, now());
    }

    public function getUserInfo(User $user): bool
    {
        return $user->can(permission_name('create', GroupEnum::Timesheet))
            || $user->can(permission_name('update', GroupEnum::Timesheet));
    }
}
