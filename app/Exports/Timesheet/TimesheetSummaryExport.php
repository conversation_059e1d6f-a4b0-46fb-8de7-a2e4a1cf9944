<?php

namespace App\Exports\Timesheet;

use App\Models\User;
use App\Models\Timesheet;
use App\Models\AttendanceExplanation;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class TimesheetSummaryExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    private $fromDate;
    private $toDate;
    private $conditions;

    public function __construct($conditions = [])
    {
        $this->conditions = $conditions;
        $this->fromDate = Carbon::parse($conditions['from_date'] ?? now()->startOfMonth());
        $this->toDate = Carbon::parse($conditions['to_date'] ?? now()->endOfMonth());
    }

    public function collection()
    {
        // Lấy danh sách users
        $users = User::query()
            ->when(isset($this->conditions['user_ids']), function ($query) {
                return $query->whereIn('id', $this->conditions['user_ids']);
            })
            ->when(isset($this->conditions['department_ids']), function ($query) {
                return $query->whereIn('staff_department_id', $this->conditions['department_ids']);
            })
            ->with(['staffDepartment'])
            ->orderBy('staff_department_id')
            ->orderBy('staff_department_chief_flag', 'desc')
            ->orderBy('name')
            ->get();

        $data = collect();

        foreach ($users as $user) {
            $summaryData = $this->calculateUserSummary($user);
            $data->push($summaryData);
        }

        return $data;
    }

    private function calculateUserSummary($user)
    {
        // Lấy tất cả timesheet trong khoảng thời gian
        $timesheets = Timesheet::where('user_id', $user->id)
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->with(['shift'])
            ->get();

        // Lấy giải trình đã được duyệt
        $approvedExplanations = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$this->fromDate, $this->toDate])
            ->where('final_status', 'approved')
            ->get();

        // Tính toán các chỉ số
        $probationWorkdays = 0;
        $holidayWorkdays = $this->calculateHolidayWorkdays();
        $onlineWorkdays = 0;
        $actualWorkdays = 0;
        $sundayWorkdays = 0;
        $totalPenalty = 0;
        $overtimeHours = 0;

        foreach ($timesheets as $timesheet) {
            $date = Carbon::parse($timesheet->date);
            
            // Kiểm tra có giải trình được duyệt không
            $explanation = $approvedExplanations->where('date', $timesheet->date)->first();
            
            if ($explanation) {
                // Có giải trình được duyệt
                if ($explanation->explanation_type === 'overtime') {
                    $overtimeHours += $explanation->ot_hours ?? 0;
                } elseif ($explanation->explanation_type === 'remote_work') {
                    $onlineWorkdays += 1;
                } else {
                    // Các loại giải trình khác được tính 1 công
                    if ($this->isInProbationPeriod($user, $date)) {
                        $probationWorkdays += 1;
                    } else {
                        $actualWorkdays += 1;
                    }
                }
            } else {
                // Không có giải trình, tính công từ máy
                $workday = $this->calculateWorkday($timesheet);
                
                if ($this->isInProbationPeriod($user, $date)) {
                    $probationWorkdays += $workday;
                } else {
                    $actualWorkdays += $workday;
                }
                
                // Tính phạt nếu có vi phạm
                $penalty = $this->calculatePenalty($timesheet, $user);
                $totalPenalty += $penalty;
            }
            
            // Tính làm việc chủ nhật
            if ($date->dayOfWeek === Carbon::SUNDAY) {
                $sundayWorkdays += 1;
            }
        }

        $totalWorkdays = $probationWorkdays + $holidayWorkdays + $onlineWorkdays + $actualWorkdays;

        return [
            'code' => $user->code ?: '',
            'name' => $user->name,
            'department' => $user->staffDepartment->name ?? '',
            'start_date' => $user->start_date ? Carbon::parse($user->start_date)->format('d/m/Y') : '',
            'probation_end_date' => $user->probation_end_date ? Carbon::parse($user->probation_end_date)->format('d/m/Y') : '',
            'end_date' => $user->end_date ? Carbon::parse($user->end_date)->format('d/m/Y') : '',
            'probation_workdays' => number_format($probationWorkdays, 2),
            'holiday_workdays' => number_format($holidayWorkdays, 2),
            'online_workdays' => number_format($onlineWorkdays, 2),
            'actual_workdays' => number_format($actualWorkdays, 2),
            'total_workdays' => number_format($totalWorkdays, 2),
            'sunday_workdays' => $sundayWorkdays,
            'total_penalty' => $totalPenalty,
            'overtime_hours' => number_format($overtimeHours, 1),
        ];
    }

    private function calculateHolidayWorkdays()
    {
        // Ngày giỗ tổ 10/3 và ngày 30/04
        $holidays = [
            $this->fromDate->year . '-03-10', // Giỗ tổ Hùng Vương
            $this->fromDate->year . '-04-30', // 30/4
        ];
        
        $holidayCount = 0;
        foreach ($holidays as $holiday) {
            $holidayDate = Carbon::parse($holiday);
            if ($holidayDate->between($this->fromDate, $this->toDate)) {
                $holidayCount++;
            }
        }
        
        return $holidayCount;
    }

    private function isInProbationPeriod($user, $date)
    {
        if (!$user->start_date || !$user->probation_end_date) {
            return false;
        }
        
        $startDate = Carbon::parse($user->start_date);
        $probationEndDate = Carbon::parse($user->probation_end_date);
        
        return $date->between($startDate, $probationEndDate);
    }

    private function calculateWorkday($timesheet)
    {
        // Logic tính công đơn giản, có thể sử dụng logic từ TimesheetRows
        if (!$timesheet->checkin || !$timesheet->checkout) {
            return 0;
        }
        
        // Tạm thời return 1 công, có thể cải thiện logic này
        return 1.0;
    }

    private function calculatePenalty($timesheet, $user)
    {
        // Logic tính phạt đơn giản, có thể sử dụng logic từ TimesheetRows
        // Tạm thời return 0, sẽ implement chi tiết sau
        return 0;
    }

    public function headings(): array
    {
        return [
            'Mã Nhân Viên',
            'Họ và Tên', 
            'Phòng ban',
            'Ngày vào',
            'Ngày kết thúc thử việc',
            'Ngày nghỉ việc',
            'Công thử việc',
            'Ngày Lễ Hưởng Lương (Ngày giỗ tổ 10/3 và ngày 30/04)',
            'CÔNG LÀM ONLINE',
            'CÔNG THỰC TẾ',
            'TỔNG CÔNG',
            'ĐI LÀM NGÀY CHỦ NHẬT',
            'Tổng phạt',
            'Tăng ca',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Header styling
        $sheet->getStyle('A1:N1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 11],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'D9E2F3']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Data styling
        $lastRow = $sheet->getHighestRow();
        $sheet->getStyle('A2:N' . $lastRow)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER
            ]
        ]);

        // Number columns alignment
        $sheet->getStyle('G2:N' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, // Mã NV
            'B' => 20, // Họ tên
            'C' => 15, // Phòng ban
            'D' => 12, // Ngày vào
            'E' => 15, // Ngày KT thử việc
            'F' => 12, // Ngày nghỉ việc
            'G' => 12, // Công thử việc
            'H' => 20, // Ngày lễ
            'I' => 15, // Công online
            'J' => 12, // Công thực tế
            'K' => 12, // Tổng công
            'L' => 15, // Chủ nhật
            'M' => 12, // Tổng phạt
            'N' => 10, // Tăng ca
        ];
    }

    public function title(): string
    {
        return 'Báo cáo tổng hợp công';
    }
}
