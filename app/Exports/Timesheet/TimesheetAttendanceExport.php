<?php

namespace App\Exports\Timesheet;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class TimesheetAttendanceExport implements FromView, WithColumnFormatting, WithStyles, ShouldAutoSize
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view('exports.timesheet.attendance-export', $this->data);
    }

    /**
     * Định dạng cột
     */
    public function columnFormats(): array
    {
        return [
            'G' => NumberFormat::FORMAT_DATE_TIME4, // Giờ vào
            'H' => NumberFormat::FORMAT_DATE_TIME4, // Giờ ra
            'I' => NumberFormat::FORMAT_NUMBER_00, // Công máy
            'M' => NumberFormat::FORMAT_NUMBER_00, // Công
            'N' => NumberFormat::FORMAT_NUMBER, // Số phút đi muộn
            'P' => NumberFormat::FORMAT_CURRENCY_USD, // Phạt đi muộn
            'Q' => NumberFormat::FORMAT_NUMBER, // Số phút về sớm
            'S' => NumberFormat::FORMAT_CURRENCY_USD, // Phạt về sớm
            'T' => NumberFormat::FORMAT_NUMBER, // Số lần vi phạm
            'U' => NumberFormat::FORMAT_CURRENCY_USD, // Tổng tiền phạt
            'V' => NumberFormat::FORMAT_NUMBER_00, // Phạt công
            'W' => NumberFormat::FORMAT_NUMBER_00, // Tổng công
            'X' => NumberFormat::FORMAT_NUMBER_00, // Tăng ca
            'Z' => NumberFormat::FORMAT_DATE_TIME4, // Giờ vào (dự kiến)
            'AA' => NumberFormat::FORMAT_DATE_TIME4, // Giờ ra (dự kiến)
            'AC' => NumberFormat::FORMAT_DATE_TIME4, // Giờ vào phụ
            'AD' => NumberFormat::FORMAT_DATE_TIME4, // Giờ ra phụ
        ];
    }

    /**
     * Định dạng style cho worksheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Header row
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE6E6FA',
                    ],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
            ],
            // All data rows
            'A:AD' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ],
            // Center align for specific columns
            'A:A' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Mã NV
            'E:F' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Ngày, Thứ
            'G:H' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Giờ vào/ra
            'I:I' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Công máy
            'M:M' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Công
            'N:X' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Các cột số liệu
            'Y:AD' => ['alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]], // Ca làm việc và giờ
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function batchSize(): int
    {
        return 1000;
    }
}
