<?php

namespace App\Exports\Timesheet\TimesheetSheet;

use App\Services\Timesheet\ShiftLogicType\LogicTypeFactory;
use App\Models\AttendanceExplanation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Time;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Generator;

trait TimesheetRows
{
    protected int $timesheetRowsCount = 0;

    public function startCell(): string
    {
        return 'A3';
    }

    public function generator(): Generator
    {
        $timesheetGroupByUsers = $this->getExportQuery()
            ->get()
            ->groupBy('user_id');

        $fromDate = Carbon::createFromFormat(
            config('common.datetime.format.database.date'),
            $this->timesheetConditions['from_date']
        );
        $toDate = Carbon::createFromFormat(
            config('common.datetime.format.database.date'),
            $this->timesheetConditions['to_date']
        );
        $diffInDays = $toDate->diffInDays($fromDate);

        $identityUserNumber = 0;

        foreach ($timesheetGroupByUsers as $timesheetGroupByUser) {
            $identityUserNumber++;

            for ($day = 0; $day <= $diffInDays; $day++) {
                $currentDate = $fromDate->copy()->addDays($day)->startOfDay();
                $identityNumber = $day === 0 ? $identityUserNumber : null;

                if (!$firstUserTimesheet = $timesheetGroupByUser->first()) {
                    continue;
                }

                $dayUserTimesheet = $timesheetGroupByUser
                    ->filter(function ($timesheet) use ($currentDate) {
                        return $currentDate->eq($timesheet->date);
                    })->first();

                if ($dayUserTimesheet) {
                    yield $this->mapRow($identityNumber, $dayUserTimesheet);
                } else {
                    yield $this->mapEmptyRow(
                        $identityNumber,
                        $firstUserTimesheet->user_code,
                        $firstUserTimesheet->user_name,
                        $firstUserTimesheet->user_department,
                        $firstUserTimesheet->user_department_chief_flag,
                        $currentDate
                    );
                }

                $this->timesheetRowsCount++;
            }
        }
    }

    protected function getExportQuery(): Builder
    {
        return $this->timesheetRepository->queryToExport($this->timesheetConditions);
    }

    public function mapRow($identityUserNumberDisplay, $timesheet): array
    {
        // Lấy giải trình cho ngày này
        $explanations = $this->getAttendanceExplanations($timesheet->user_id, $timesheet->date);

        return $this->map(
            $identityUserNumberDisplay,
            $timesheet->user_code,
            $timesheet->user_name,
            $timesheet->user_department,
            $this->getPositionValue($timesheet->user_department_chief_flag),
            $timesheet->date,
            $this->getCheckinValue($timesheet->checkin),
            $this->getCheckoutValue($timesheet->checkout),
            $this->getWorkDayValue($timesheet),
            $timesheet->shift_name,
            $timesheet,
            $explanations
        );
    }

    public function mapEmptyRow(
        $identityUserNumberDisplay,
        $userCode,
        $userName,
        $userDepartment,
        $userStaffDepartmentChiefFlag,
        Carbon $date
    ): array {
        return $this->map(
            $identityUserNumberDisplay,
            $userCode,
            $userName,
            $userDepartment,
            $this->getPositionValue($userStaffDepartmentChiefFlag),
            $date
        );
    }

    public function map(
        $identityUserNumberDisplay,
        $userCode,
        $userName,
        $userDepartment,
        $userStaffDepartmentChiefFlag,
        Carbon $date,
        $checkin = null,
        $checkout = null,
        $workdayValue = 0,
        $shiftName = null,
        $timesheet = null,
        $explanations = null
    ): array {
        // Tính toán dữ liệu từ giải trình và vi phạm
        $attendanceData = $this->calculateAttendanceData($timesheet, $explanations, $userStaffDepartmentChiefFlag);

        return [
            $identityUserNumberDisplay,
            $userCode,
            $userName,
            $userDepartment,
            $userStaffDepartmentChiefFlag,
            Date::dateTimeToExcel($date),
            get_day_of_weeks($date),
            $checkin,
            $checkout,
            $workdayValue,
            $shiftName,
            $attendanceData['attendance_status'],           // Xác nhận tình trạng chấm công
            $attendanceData['explanation_summary'],         // Giải trình công
            $attendanceData['final_workday'],              // Công (sau điều chỉnh)
            $attendanceData['late_minutes'],               // Số phút đi muộn
            $attendanceData['late_explanation'],           // GT đi muộn
            $attendanceData['late_penalty'],               // Phạt đi muộn
            $attendanceData['early_minutes'],              // Số phút về sớm
            $attendanceData['early_explanation'],          // GT về sớm
            $attendanceData['early_penalty'],              // Phạt về sớm
            $attendanceData['violation_count'],            // Số lần vi phạm
            $attendanceData['total_penalty'],              // Tổng tiền phạt
            $attendanceData['penalty_workday'],            // Phạt công
            $attendanceData['total_workday'],              // Tổng công
            $attendanceData['overtime_hours'],             // Tăng ca
        ];
    }

    protected function getPositionValue($isChief): string
    {
        $configKey = $isChief
            ? 'department_chief_label'
            : 'staff_label';

        return config('export.staff_timesheets.' . $configKey);
    }

    protected function getCheckinValue($checkin)
    {
        if (!$checkin) {
            return null;
        }

        return Time::fromHMS(
            $checkin->format('H'),
            $checkin->format('i'),
            $checkin->format('s')
        );
    }

    protected function getCheckoutValue($checkout)
    {
        if (!$checkout) {
            return null;
        }

        return Time::fromHMS(
            $checkout->format('H'),
            $checkout->format('i'),
            $checkout->format('s')
        );
    }

    protected function getWorkDayValue($timesheet): float
    {
        // Nếu 1 ngày user có 2 lần chấm công thì
        // sẽ để công của ngày đó = 0 luôn.
        if ($timesheet->timsheets_unique_date_user_id_count > 1) {
            return 0;
        }

        return LogicTypeFactory::create()->calcWorkday([
            'schedule_start_time' => $timesheet->shift_start_time,
            'schedule_end_time' => $timesheet->shift_end_time,
            'start_time' => $timesheet->checkin?->format('H:i:s'),
            'end_time' => $timesheet->checkout?->format('H:i:s'),
            'workday_min_1' => $timesheet->shift_workday_min_1,
            'workday_min_2' => $timesheet->shift_workday_min_2,
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_DATE_DDMMYYYY,  // Ngày
            'H' => 'hh:mm',                             // Giờ vào
            'I' => 'hh:mm',                             // Giờ ra
            'J' => '0.0',                               // Công
            'N' => '0.0',                               // Công (sau điều chỉnh)
            'O' => '0',                                 // Số phút đi muộn
            'Q' => '#,##0',                             // Phạt đi muộn
            'R' => '0',                                 // Số phút về sớm
            'T' => '#,##0',                             // Phạt về sớm
            'U' => '0',                                 // Số lần vi phạm
            'V' => '#,##0',                             // Tổng tiền phạt
            'W' => '0.0',                               // Phạt công
            'X' => '0.0',                               // Tổng công
            'Y' => '0.0',                               // Tăng ca
        ];
    }

    protected function getTimesheetRowsCount(): int
    {
        return $this->timesheetRowsCount;
    }

    /**
     * Lấy giải trình chấm công cho user và ngày cụ thể
     */
    private function getAttendanceExplanations($userId, $date)
    {
        return AttendanceExplanation::where('user_id', $userId)
            ->whereDate('date', $date)
            ->where('final_status', 'approved')
            ->get();
    }

    /**
     * Tính toán dữ liệu chấm công từ timesheet và giải trình
     */
    private function calculateAttendanceData($timesheet, $explanations, $isChief)
    {
        $data = [
            'attendance_status' => 'Bình thường',
            'explanation_summary' => '',
            'final_workday' => 0,
            'late_minutes' => 0,
            'late_explanation' => '',
            'late_penalty' => 0,
            'early_minutes' => 0,
            'early_explanation' => '',
            'early_penalty' => 0,
            'violation_count' => 0,
            'total_penalty' => 0,
            'penalty_workday' => 0,
            'total_workday' => 0,
            'overtime_hours' => 0,
        ];

        if (!$timesheet) {
            return $data;
        }

        // Công cơ bản từ timesheet
        $data['final_workday'] = $this->getWorkDayValue($timesheet);
        $data['total_workday'] = $data['final_workday'];

        // Xử lý giải trình nếu có
        if ($explanations && $explanations->count() > 0) {
            $data = $this->applyExplanations($data, $explanations, $timesheet);
        } else {
            // Tính vi phạm nếu không có giải trình
            $data = $this->calculateViolations($data, $timesheet, $isChief);
        }

        return $data;
    }

    /**
     * Áp dụng các giải trình đã được duyệt
     */
    private function applyExplanations($data, $explanations, $timesheet)
    {
        $explanationTexts = [];
        $totalOtHours = 0;
        $hasWorkdayAdjustment = false;

        foreach ($explanations as $explanation) {
            $explanationTexts[] = $explanation->explanation_type_text . ': ' . $explanation->explanation;

            switch ($explanation->explanation_type) {
                case 'late':
                    $data['late_explanation'] = $explanation->explanation;
                    $data['attendance_status'] = 'Đã giải trình đi muộn';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'early':
                    $data['early_explanation'] = $explanation->explanation;
                    $data['attendance_status'] = 'Đã giải trình về sớm';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'insufficient_hours':
                case 'no_checkin':
                case 'no_checkout':
                    $data['attendance_status'] = 'Đã giải trình thiếu giờ';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'overtime':
                    $totalOtHours += $explanation->ot_hours ?? 0;
                    $data['attendance_status'] = 'Có tăng ca';
                    break;

                case 'remote_work':
                    $data['attendance_status'] = 'Làm việc từ xa';
                    $hasWorkdayAdjustment = true;
                    break;

                case 'other':
                    $data['attendance_status'] = 'Đã giải trình khác';
                    $hasWorkdayAdjustment = true;
                    break;
            }
        }

        // Cập nhật thông tin tổng hợp
        $data['explanation_summary'] = implode('; ', $explanationTexts);
        $data['overtime_hours'] = $totalOtHours;

        // Nếu có giải trình được duyệt, điều chỉnh công
        if ($hasWorkdayAdjustment) {
            $data['final_workday'] = 1.0; // Giải trình được duyệt = 1 công
            $data['total_workday'] = $data['final_workday'];
        }

        return $data;
    }

    /**
     * Tính toán vi phạm khi không có giải trình
     */
    private function calculateViolations($data, $timesheet, $isChief)
    {
        if (!$timesheet->checkin || !$timesheet->checkout || !$timesheet->shift_start_time || !$timesheet->shift_end_time) {
            return $data;
        }

        $checkin = Carbon::parse($timesheet->checkin);
        $checkout = Carbon::parse($timesheet->checkout);

        // Kết hợp ngày từ timesheet với thời gian từ shift
        $timesheetDate = Carbon::parse($timesheet->date);
        $expectedCheckin = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_start_time);
        $expectedCheckout = $timesheetDate->copy()->setTimeFromTimeString($timesheet->shift_end_time);

        // Tính đi muộn
        if ($checkin->gt($expectedCheckin)) {
            $data['late_minutes'] = $checkin->diffInMinutes($expectedCheckin);
            $data['late_penalty'] = $this->calculatePenalty($data['late_minutes'], $isChief);
            $data['violation_count']++;
        }

        // Tính về sớm
        if ($checkout->lt($expectedCheckout)) {
            $data['early_minutes'] = $expectedCheckout->diffInMinutes($checkout);
            $data['early_penalty'] = $this->calculatePenalty($data['early_minutes'], $isChief);
            $data['violation_count']++;
        }

        // Tính tổng phạt
        $data['total_penalty'] = $data['late_penalty'] + $data['early_penalty'];

        // Cập nhật trạng thái
        if ($data['late_minutes'] > 0 || $data['early_minutes'] > 0) {
            $data['attendance_status'] = 'Có vi phạm';
        }

        return $data;
    }

    /**
     * Tính tiền phạt theo bảng phạt và cấp bậc
     */
    private function calculatePenalty($minutes, $isChief)
    {
        if ($minutes <= 1) {
            return 0;
        }

        // Bảng phạt cho Nhân viên
        if (!$isChief) {
            if ($minutes >= 1.01 && $minutes <= 10) return 50000;
            if ($minutes >= 11.01 && $minutes <= 30) return 100000;
            if ($minutes >= 31.01 && $minutes <= 60) return 120000;
            if ($minutes >= 61.01 && $minutes <= 120) return 200000;
            if ($minutes > 120) return 0; // Không phạt tiền, có thể có hình thức khác
        }
        // Bảng phạt cho Trưởng nhóm
        else {
            if ($minutes >= 1.01 && $minutes <= 10) return 50000;
            if ($minutes >= 11.01 && $minutes <= 30) return 100000;
            if ($minutes >= 31.01 && $minutes <= 60) return 150000;
            if ($minutes >= 61.01 && $minutes <= 120) return 250000;
            if ($minutes > 120) return 0; // Không phạt tiền, có thể có hình thức khác
        }

        return 0;
    }
}
