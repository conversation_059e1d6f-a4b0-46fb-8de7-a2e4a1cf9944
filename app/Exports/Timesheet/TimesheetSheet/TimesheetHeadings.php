<?php

namespace App\Exports\Timesheet\TimesheetSheet;

use Maatwebsite\Excel\Sheet;
use PhpOffice\PhpSpreadsheet\Helper\Dimension;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

trait TimesheetHeadings
{
    protected int $headingsLastColumnIndex;
    protected int $headingsRowsCount;

    protected function getHeadings(): array
    {
        return [
            [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                'Viết lại',
                'PHẦN ĐI MUỘN',
                null,
                null,
                'PHẦN VỀ SỚM',
                null,
                null,
                'TỔNG CỘNG',
                null,
                null,
                null,
                'PHẦN TĂNG CA',
                null,
                null,
                'THÔNG TIN CA RULE',
            ],
            [
                null,
                'Mã nhân viên',
                'Tên nhân viên',
                'Phòng ban',
                'Chức vụ',
                '<PERSON><PERSON>y',
                '<PERSON>h<PERSON>',
                '<PERSON><PERSON><PERSON> vào',
                '<PERSON><PERSON><PERSON> ra',
                '<PERSON>ông',
                '<PERSON>a',
                '<PERSON><PERSON><PERSON> nhận tình trạng chấm công',
                '<PERSON><PERSON><PERSON><PERSON> trình công',
                '<PERSON><PERSON>ng',
                '<PERSON><PERSON> phút đi muộn',
                'GT đi muộn',
                'Phạt đi muộn',
                'Số phút về sớm',
                'GT về sớm',
                'Phạt về sớm',
                'Số lần vi phạm',
                'Tổng tiền phạt',
                'Phat công',
                'Tổng công',
                'Tăng ca',
                'Giờ nghỉ',
                'Hiệu lực từ',
                'Quy tắc khác',
            ],
        ];
    }

    protected function drawHeadings(Sheet $sheet): void
    {
        $headings = $this->getHeadings();

        $sheet->append($headings, 'A1');

        $headingCoordinate1 = [1, 1, count($headings[0]), 1];
        $headingCoordinate2 = [1, 2, count($headings[1]), 2];

        $sheet->getRowDimension(1)->setRowHeight(0.35, Dimension::UOM_INCHES);
        $sheet->getRowDimension(2)->setRowHeight(0.85, Dimension::UOM_INCHES);

        $sheet->getStyle($headingCoordinate1)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER)
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setWrapText(true);
        $sheet->getStyle($headingCoordinate1)->getFont()->setBold(true);
        $sheet->getStyle($headingCoordinate2)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER)
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setWrapText(true);
        $sheet->getStyle($headingCoordinate2)->getFont()->setBold(true);

        $sheet->mergeCells('A1:K1');
        $sheet->mergeCells('O1:Q1');
        $sheet->mergeCells('R1:T1');
        $sheet->mergeCells('U1:X1');
        $sheet->mergeCells('Z1:AB1');

        $sheet->getStyle('A1')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('FFEBCD');
        $sheet->getStyle('M2')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('D9E2F3');
        $sheet->getStyle('O1')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('E2EFD9');
        $sheet->getStyle('R1')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('ED7D31');
        $sheet->getStyle('U1')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('FFE598');
        $sheet->getStyle('U2')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('FFC000');
        $sheet->getStyle('Y2')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('92D050');
        $sheet->getStyle('Z1')
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('D9E2F3');

        $sheet->getStyle('N1')
            ->getFont()
            ->getColor()
            ->setRGB('red');
        $sheet->getStyle('N2')
            ->getFont()
            ->getColor()
            ->setRGB('red');
        $sheet->getStyle('Q2')
            ->getFont()
            ->getColor()
            ->setRGB('red');
        $sheet->getStyle('T2')
            ->getFont()
            ->getColor()
            ->setRGB('red');
        $sheet->getStyle('V2')
            ->getFont()
            ->getColor()
            ->setRGB('red');

        $this->resizeColumnsWidth($sheet);
    }

    protected function getHeadingsLastColumnIndex(): int
    {
        if (!isset($this->headingsLastColumnIndex)) {
            $headings = $this->getHeadings();

            $this->headingsLastColumnIndex = max(count($headings[0]), count($headings[1]));
        }

        return $this->headingsLastColumnIndex;
    }

    protected function getHeadingsRowsCount(): int
    {
        if (!isset($this->headingsRowsCount)) {
            $this->headingsRowsCount = count($this->getHeadings());
        }

        return $this->headingsRowsCount;
    }

    protected function resizeColumnsWidth(Sheet $sheet): void
    {
        $widthOfColumns = [
            1 => 0.54, // STT
            2 => 1.24, // Ma NV
            3 => 2.52, // Ten NV
            4 => 1.48, // Phong ban
            5 => 1.23, // Chuc vu
            6 => 1.20, // Ngay
            7 => 0.60, // Thu
            8 => 0.64, // Gio vao
            9 => 0.64, // Gio ra
            10 => 0.61, // Cong
            11 => 0.91, // Ca
            12 => 1.80, // Xac nhan tinh trang cham cong
            13 => 1.15, // Giai trinh cong
            14 => 0.80, // Cong
            15 => 0.90, // So phut di muon
            16 => 0.90, // GT di muon
            17 => 0.88, // Phat di muon
            18 => 0.90, // So phut ve som
            19 => 0.90, // GT ve som
            20 => 0.88, // Phat ve som
            21 => 0.79, // So lan vi pham
            22 => 0.90, // Tong tien phat
            23 => 0.71, // Phat cong
            24 => 0.90, // Tong cong
            25 => 1.25, // Tang ca
            26 => 1.50, // Gio nghi (AA)
            27 => 1.20, // Hieu luc tu (AB)
            28 => 1.50, // Quy tac khac (AC)
        ];

        foreach ($widthOfColumns as $columnIndex => $widthOfColumn) {
            $sheet->getColumnDimensionByColumn($columnIndex)->setWidth($widthOfColumn, Dimension::UOM_INCHES);
        }
    }
}
