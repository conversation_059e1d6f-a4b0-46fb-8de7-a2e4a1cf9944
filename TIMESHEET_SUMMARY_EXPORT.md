# Export Tổng Hợp Công - <PERSON><PERSON><PERSON>o Theo Nhân Viên

## Tổng quan
Đã tạo thêm chức năng export tổng hợp công theo nhân viên với format báo cáo tổng hợp thay vì chi tiết theo ngày.

## Format báo cáo mới

### Các cột trong báo cáo:
1. **Mã Nhân Viên** - User code
2. **Họ và Tên** - Tên đầy đủ
3. **Phòng ban** - Tên phòng ban
4. **Ngày vào** - <PERSON><PERSON>y bắt đầu làm việc
5. **<PERSON><PERSON>y kết thúc thử việc** - <PERSON><PERSON><PERSON> hết thử việc
6. **Ngày nghỉ việc** - Ngày nghỉ việc (nếu có)
7. **Công thử việc** - <PERSON><PERSON> công trong thời gian thử việc
8. **<PERSON><PERSON><PERSON> Hưởng Lương** - Ngày giỗ tổ 10/3 và ngày 30/04
9. **CÔNG LÀM ONLINE** - <PERSON><PERSON> công làm việc từ xa
10. **CÔNG THỰC TẾ** - Số công làm việc thực tế
11. **TỔNG CÔNG** - Tổng số công
12. **ĐI LÀM NGÀY CHỦ NHẬT** - Số ngày làm chủ nhật
13. **Tổng phạt** - Tổng tiền phạt vi phạm
14. **Tăng ca** - Tổng giờ tăng ca

## Files đã tạo/cập nhật

### 1. TimesheetSummaryExport.php
**File**: `app/Exports/Timesheet/TimesheetSummaryExport.php`

**Chức năng chính:**
- Export theo format tổng hợp nhân viên
- Tính toán các chỉ số công, phạt, tăng ca
- Styling Excel với header và borders
- Group theo phòng ban và sắp xếp theo tên

**Methods chính:**
```php
public function collection() // Lấy dữ liệu users và tính toán
private function calculateUserSummary($user) // Tính tổng hợp cho từng user
private function calculateHolidayWorkdays() // Tính ngày lễ hưởng lương
private function isInProbationPeriod($user, $date) // Kiểm tra thử việc
private function calculateWorkday($timesheet) // Tính công từ timesheet
private function calculatePenalty($timesheet, $user) // Tính phạt
```

### 2. TimesheetService.php
**File**: `app/Services/Timesheet/TimesheetService.php`

**Method mới:**
```php
public function exportSummary(array $conditions)
{
    $currentTime = now();
    $nowFormat = $currentTime->format(config('common.datetime.format.client.date_download'));
    $fileName = $nowFormat . '_bao_cao_tong_hop_cong.xlsx';

    return \Maatwebsite\Excel\Facades\Excel::download(
        new \App\Exports\Timesheet\TimesheetSummaryExport($conditions), 
        $fileName
    );
}
```

### 3. TimesheetController.php
**File**: `app/Http/Controllers/TimesheetController.php`

**Method mới:**
```php
public function exportSummary(ExportTimesheetRequest $exportTimesheetRequest)
{
    $this->authorize('export', Timesheet::class);
    $conditions = $exportTimesheetRequest->validated();
    return $this->timesheetService->exportSummary($conditions);
}
```

### 4. Routes
**File**: `routes/web.php`

**Route mới:**
```php
Route::get('timesheets/export-summary', [\App\Http\Controllers\TimesheetController::class, 'exportSummary'])
    ->name('timesheets.export-summary');
```

### 5. View
**File**: `resources/views/pages/timesheets/index.blade.php`

**UI mới:**
```html
<!-- 2 buttons export -->
<a id="timesheet-export" href="#" class="btn btn-primary">Export Chi Tiết</a>
<a id="timesheet-export-summary" href="#" class="btn btn-success">Export Tổng Hợp</a>
```

**JavaScript:**
```javascript
$(document).on('click', '#timesheet-export-summary', function(e) {
    e.preventDefault();
    var formData = $('#filter-form').serialize();
    window.location.href = EXPORT_SUMMARY_URL + '?' + formData;
});
```

## Logic tính toán

### 1. **Công thử việc:**
- Kiểm tra ngày trong khoảng `start_date` đến `probation_end_date`
- Tính tổng công trong thời gian thử việc

### 2. **Ngày lễ hưởng lương:**
- **10/3**: Ngày giỗ tổ Hùng Vương
- **30/4**: Ngày giải phóng miền Nam
- Tính số ngày lễ trong khoảng thời gian export

### 3. **Công làm online:**
- Từ giải trình loại `remote_work` được duyệt
- Mỗi giải trình = 1 công online

### 4. **Công thực tế:**
- Tính từ timesheet có checkin/checkout
- Trừ đi công thử việc và công online
- Áp dụng giải trình được duyệt

### 5. **Chủ nhật:**
- Đếm số ngày chủ nhật có checkin/checkout
- Không phân biệt có làm việc hay không

### 6. **Tổng phạt:**
- Tính từ vi phạm không có giải trình
- Áp dụng quy tắc x2 nếu >7 vi phạm/tháng

### 7. **Tăng ca:**
- Từ giải trình loại `overtime` được duyệt
- Tổng `ot_hours` trong khoảng thời gian

## Styling Excel

### **Headers:**
- Background: Light blue (#D9E2F3)
- Font: Bold, size 11
- Alignment: Center, wrap text
- Borders: Thin borders

### **Data:**
- Borders: Thin borders
- Number columns: Right alignment
- Text columns: Left alignment

### **Column widths:**
- Mã NV: 12
- Họ tên: 20
- Phòng ban: 15
- Ngày: 12-15
- Số liệu: 10-12

## Ví dụ dữ liệu

```
Mã NV    | Họ và Tên           | Phòng ban    | Công thử việc | Công thực tế | Tổng công | Tổng phạt | Tăng ca
---------|--------------------|--------------|--------------|--------------|-----------|-----------|---------
TL0005   | Cao Trung Hiếu     | Trợ lý TGĐ   | 0.00         | 24.00        | 26.00     | 0         | 0.0
LIT001   | Nguyễn Văn Tài     | Trưởng Phòng | 0.00         | 24.00        | 26.00     | 0         | 0.0
LIT002   | Văn Đức Hải Trung  | Nhân Viên    | 23.50        | 23.50        | 25.50     | 0         | 17.5
```

## Permissions

### **Sử dụng permission hiện có:**
- `export timesheet` - Quyền export timesheets
- Không cần permission mới
- Cùng authorization với export chi tiết

## Performance

### **Optimizations:**
- Eager load `staffDepartment` và `shift`
- Group queries theo user
- Sử dụng Collection để xử lý dữ liệu
- Lazy loading cho explanations

### **Memory usage:**
- Load từng user một để tránh memory overflow
- Không cache dữ liệu lớn
- Stream processing cho file lớn

## Test cases

### **Case 1: User thử việc**
- Có `start_date` và `probation_end_date`
- Công trong thời gian thử việc tính riêng

### **Case 2: User chính thức**
- Không có `probation_end_date` hoặc đã hết thử việc
- Tất cả công tính vào "Công thực tế"

### **Case 3: User có giải trình**
- Remote work → Công online
- Overtime → Tăng ca
- Các loại khác → Công thực tế

### **Case 4: User có vi phạm**
- Tính phạt theo bảng phạt
- Áp dụng x2 nếu >7 vi phạm/tháng

## Mở rộng tương lai

### 1. **Thêm cột:**
- Phụ cấp
- Lương cơ bản
- Thưởng
- Khấu trừ

### 2. **Group theo:**
- Chi nhánh
- Cấp bậc
- Loại hợp đồng

### 3. **Tính toán nâng cao:**
- Công theo ca
- Phân loại OT
- Tính lương

## Kết luận

✅ **Đã hoàn thành:** Export tổng hợp công theo nhân viên với 14 cột dữ liệu

🎯 **Kết quả:** 2 loại export - Chi tiết (theo ngày) và Tổng hợp (theo nhân viên)

🔧 **UI:** 2 buttons riêng biệt với màu sắc khác nhau để phân biệt
