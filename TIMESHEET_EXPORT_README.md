# Hướng dẫn sử dụng chức năng Export Bảng Chấm Công

## Tổng quan
Chức năng export bảng chấm công cho phép xuất dữ liệu chấm công đã được điều chỉnh theo các giải trình đã được duyệt ra file Excel với định dạng chi tiết.

## Các file đã tạo

### 1. Export Class
- **File**: `app/Exports/Timesheet/TimesheetAttendanceExport.php`
- **Chức năng**: Xử lý export Excel với định dạng và style
- **Features**:
  - Định dạng giờ (H:i) cho các cột thời gian
  - Định dạng số cho các cột tiền và công
  - Auto-size columns
  - Border và style cho bảng

### 2. Service Class
- **File**: `app/Services/Timesheet/TimesheetAttendanceExportService.php`
- **Ch<PERSON><PERSON> năng**: <PERSON><PERSON> lý logic nghiệp vụ
- **Features**:
  - L<PERSON>y dữ liệu timesheet theo tháng/năm
  - Áp dụng các giải trình đã được duyệt
  - Tính toán vi phạm và phạt tiền
  - Xử lý ca làm việc từ xa

### 3. Controller
- **File**: `app/Http/Controllers/TimesheetAttendanceController.php`
- **Routes**:
  - `GET /timesheet/attendance-export` - Trang chính
  - `GET /timesheet/attendance-export/excel` - Export Excel
  - `GET /timesheet/attendance-export/preview` - Xem trước dữ liệu
  - `GET /timesheet/attendance-export/users-by-department` - API lấy user theo phòng ban

### 4. Views
- **File**: `resources/views/pages/timesheet/attendance-export.blade.php` - Trang chính
- **File**: `resources/views/exports/timesheet/attendance-export.blade.php` - Template Excel

### 5. Config
- **File**: `config/export.php` - Thêm config cho timesheet export

## Cấu trúc dữ liệu Excel

### Các cột trong file Excel:
1. **Mã nhân viên** - User code
2. **Tên nhân viên** - Tên đầy đủ
3. **Phòng ban** - Staff department
4. **Chức vụ** - Position từ relationship
5. **Ngày** - Ngày chấm công (dd/mm/yyyy)
6. **Thứ** - Thứ trong tuần (tiếng Việt)
7. **Giờ vào** - Check-in time (H:i)
8. **Giờ ra** - Check-out time (H:i)
9. **Công máy** - Workday tính từ máy
10. **Ca làm việc** - Tên ca làm việc
11. **Xác nhận tình trạng chấm công** - Trạng thái
12. **Giải trình công** - Tóm tắt các giải trình
13. **Công** - Công cuối cùng sau điều chỉnh
14. **Số phút đi muộn** - Minutes late
15. **GT đi muộn** - Giải trình đi muộn
16. **Phạt đi muộn** - Tiền phạt đi muộn
17. **Số phút về sớm** - Minutes early leave
18. **GT về sớm** - Giải trình về sớm
19. **Phạt về sớm** - Tiền phạt về sớm
20. **Số lần vi phạm** - Tổng số vi phạm
21. **Tổng tiền phạt** - Tổng tiền phạt
22. **Phạt công** - Công bị phạt
23. **Tổng công** - Tổng công cuối cùng
24. **Tăng ca** - Giờ tăng ca
25. **Ca làm việc (mã)** - Shift code
26. **Giờ vào (dự kiến)** - Expected check-in
27. **Giờ ra (dự kiến)** - Expected check-out
28. **Ca làm việc phụ** - Remote shift name
29. **Giờ vào phụ** - Remote check-in
30. **Giờ ra phụ** - Remote check-out

## Logic xử lý

### 1. Áp dụng giải trình đã duyệt
- Lấy tất cả giải trình có `final_status = 'approved'`
- Điều chỉnh công theo loại giải trình:
  - `late`, `early`, `insufficient_hours`, `no_checkin`, `no_checkout`, `remote_work`, `other`: 1 công
  - `overtime`: Không ảnh hưởng công, chỉ thêm giờ OT

### 2. Tính toán vi phạm (khi không có giải trình)
- **Đi muộn**: So sánh check-in với giờ bắt đầu ca
- **Về sớm**: So sánh check-out với giờ kết thúc ca
- **Phạt tiền**: Theo thang phạt (có thể tùy chỉnh)

### 3. Định dạng file Excel
- **Tên file**: `{ngày_xuất}_bang_cham_cong_thang_{tháng}_{năm}.xlsx`
- **Header**: Có màu nền và border
- **Dữ liệu**: Căn giữa cho số, căn trái cho text
- **Thống kê**: Tổng hợp ở cuối file

## Cách sử dụng

### 1. Truy cập trang export
```
GET /timesheet/attendance-export
```

### 2. Chọn tham số
- **Tháng**: Bắt buộc (1-12)
- **Năm**: Bắt buộc (2020-2030)
- **Phòng ban**: Tùy chọn
- **Nhân viên**: Tùy chọn (hiện khi chọn phòng ban)

### 3. Xem trước dữ liệu
- Click "Xem trước" để kiểm tra dữ liệu
- Hiển thị thống kê tổng quan
- Hiển thị 10 bản ghi đầu tiên

### 4. Export Excel
- Click "Xuất Excel" để download file
- File sẽ được tải về với tên định dạng chuẩn

## Quyền truy cập
- Cần có quyền `viewAny` trên model `Timesheet`
- Có thể tùy chỉnh thêm quyền theo yêu cầu

## Tùy chỉnh

### 1. Thang phạt tiền
Chỉnh sửa trong `TimesheetAttendanceExportService.php`:
```php
private function calculateLatePenalty(int $minutes): int
{
    if ($minutes <= 5) return 0;
    if ($minutes <= 15) return 50000;
    if ($minutes <= 30) return 100000;
    return 200000;
}
```

### 2. Định dạng Excel
Chỉnh sửa trong `TimesheetAttendanceExport.php`:
```php
public function columnFormats(): array
{
    // Thêm/sửa định dạng cột
}

public function styles(Worksheet $sheet)
{
    // Thêm/sửa style
}
```

### 3. Thêm cột mới
1. Cập nhật view template `attendance-export.blade.php`
2. Cập nhật service để cung cấp dữ liệu
3. Cập nhật định dạng trong export class

## Lưu ý
- Dữ liệu export đã bao gồm các điều chỉnh từ giải trình đã duyệt
- File Excel có thể lớn với dữ liệu nhiều, đã optimize với chunk/batch
- Hỗ trợ filter theo phòng ban và nhân viên cụ thể
- Có thống kê tổng hợp ở cuối file
