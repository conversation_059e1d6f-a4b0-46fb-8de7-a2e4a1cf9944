# Hướng dẫn cấp quyền Admin cho Export Bảng Chấm Công

## Tổng quan
Đã cấp quyền cho các role admin để truy cập chức năng export bảng chấm công chi tiết.

## Các quyền đã được cấp

### 1. Permissions mới đã thêm
- **`exportAttendance timesheet`** - Quyền xuất bảng chấm công chi tiết

### 2. Roles được cấp quyền
- **R000 (SuperAdministrator)** - C<PERSON> tất cả quyền timesheet bao gồm:
  - `viewAny timesheet`
  - `create timesheet`
  - `update timesheet`
  - `delete timesheet`
  - `export timesheet`
  - `exportAttendance timesheet` ✅ **MỚI**

- **R001 (Administrator)** - C<PERSON> tất cả quyền timesheet bao gồm:
  - `viewAny timesheet`
  - `create timesheet`
  - `update timesheet`
  - `delete timesheet`
  - `export timesheet`
  - `exportAttendance timesheet` ✅ **MỚI**

## Các file đã cập nhật

### 1. Config Permissions
**File**: `config/common_permission.php`

```php
// Thêm permission mới
\App\Enums\Permission\GroupEnum::Timesheet => [
    'viewAny' => 'Xem danh sách bảng chấm công',
    'create' => 'Tạo bảng chấm công',
    'update' => 'Sửa bảng chấm công',
    'delete' => 'Xóa bảng chấm công',
    'export' => 'Export bảng chấm công',
    'exportAttendance' => 'Export bảng chấm công chi tiết', // ✅ MỚI
],

// Cấp quyền cho SuperAdministrator (R000)
\App\Enums\Role\RoleEnum::SuperAdministrator => array_merge(
    // ... other permissions
    permission_names([
        'viewAny',
        'create',
        'update',
        'delete',
        'export',
        'exportAttendance', // ✅ MỚI
    ], \App\Enums\Permission\GroupEnum::Timesheet)
),

// Cấp quyền cho Administrator (R001)
\App\Enums\Role\RoleEnum::Administrator => array_merge(
    // ... other permissions
    permission_names([
        'viewAny',
        'create',
        'update',
        'delete',
        'export',
        'exportAttendance', // ✅ MỚI
    ], \App\Enums\Permission\GroupEnum::Timesheet)
),
```

### 2. Policy
**File**: `app/Policies/TimesheetPolicy.php`

```php
public function exportAttendance(User $user): bool
{
    return true;
}
```

### 3. Controller Authorization
**File**: `app/Http/Controllers/TimesheetAttendanceController.php`

```php
// Kiểm tra quyền trong exportExcel method
if (!$user->can('exportAttendance', \App\Models\Timesheet::class)) {
    abort(403, 'Bạn không có quyền xuất bảng chấm công chi tiết');
}

// Kiểm tra quyền trong preview method
if (!$user->can('exportAttendance', \App\Models\Timesheet::class)) {
    return response()->json([
        'success' => false,
        'message' => 'Bạn không có quyền xem dữ liệu chấm công chi tiết'
    ], 403);
}
```

## Seeders đã chạy

### 1. PermissionsSeeder
```bash
php artisan db:seed --class=PermissionsSeeder
```
- Tạo permissions mới trong database

### 2. RoleAssignPermissionsSeeder
```bash
php artisan db:seed --class=RoleAssignPermissionsSeeder
```
- Gán permissions cho các roles

## Kiểm tra quyền

### 1. Kiểm tra permissions đã tạo
```bash
php artisan tinker
>>> App\Models\Permission::where('name', 'like', '%timesheet%')->pluck('name')->toArray()
```

### 2. Kiểm tra role có quyền
```bash
php artisan tinker
>>> $admin = App\Models\Role::where('name', 'R001')->first()
>>> $admin->hasPermissionTo('exportAttendance timesheet')
```

### 3. Kiểm tra user có quyền
```bash
php artisan tinker
>>> $user = App\Models\User::find(1) // Admin user
>>> $user->can('exportAttendance', App\Models\Timesheet::class)
```

## Routes có thể truy cập

Với quyền `exportAttendance timesheet`, admin có thể truy cập:

1. **Trang chính**: `GET /timesheet/attendance-export`
2. **Export Excel**: `GET /timesheet/attendance-export/excel`
3. **Preview dữ liệu**: `GET /timesheet/attendance-export/preview`
4. **API users theo phòng ban**: `GET /timesheet/attendance-export/users-by-department`

## Cách cấp quyền cho role khác

Nếu muốn cấp quyền cho role khác, thêm vào `config/common_permission.php`:

```php
\App\Enums\Role\RoleEnum::YourRole => array_merge(
    // ... other permissions
    permission_names([
        'exportAttendance', // Chỉ cần quyền này để export
    ], \App\Enums\Permission\GroupEnum::Timesheet)
),
```

Sau đó chạy seeder:
```bash
php artisan db:seed --class=RoleAssignPermissionsSeeder
```

## Lưu ý bảo mật

- Quyền `exportAttendance` cho phép xuất toàn bộ dữ liệu chấm công chi tiết
- Dữ liệu bao gồm thông tin nhạy cảm như vi phạm, phạt tiền
- Chỉ cấp cho những role thực sự cần thiết
- Có thể thêm filter theo phòng ban để giới hạn quyền truy cập

## Trạng thái hiện tại

✅ **Đã hoàn thành:**
- Tạo permission `exportAttendance timesheet`
- Cấp quyền cho R000 (SuperAdministrator)
- Cấp quyền cho R001 (Administrator)
- Cập nhật Policy và Controller
- Chạy seeders cập nhật database
- Test quyền thành công

🎯 **Admin có thể truy cập:**
- Trang export: `/timesheet/attendance-export`
- Tất cả chức năng export bảng chấm công chi tiết
