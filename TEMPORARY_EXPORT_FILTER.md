# Thay đổi tạm thời - Export chỉ User ID 2845

## <PERSON><PERSON> tả
Đã sửa chức năng export timesheets để tạm thời chỉ xuất dữ liệu của user ID 2845.

## Thông tin User
- **User ID**: 2845
- **Tên**: Văn Đức Hải Trung
- **Mã nhân viên**: (trống)

## File đã thay đổi

### TimesheetController.php
**File**: `app/Http/Controllers/TimesheetController.php`
**Method**: `export()`

**Thay đổi:**
```php
// TRƯỚC
public function export(ExportTimesheetRequest $exportTimesheetRequest)
{
    $this->authorize('export', Timesheet::class);

    return $this->timesheetService->export($exportTimesheetRequest->validated());
}

// SAU
public function export(ExportTimesheetRequest $exportTimesheetRequest)
{
    $this->authorize('export', Timesheet::class);

    // Tạm thời chỉ export user ID 2845
    $conditions = $exportTimesheetRequest->validated();
    $conditions['user_ids'] = [2845];

    return $this->timesheetService->export($conditions);
}
```

## Tác động

### ✅ Hiện tại
- Export timesheets chỉ hiển thị dữ liệu của user ID 2845
- Tất cả filter khác (ngày, phòng ban) vẫn hoạt động bình thường
- Chỉ giới hạn theo user

### 🔄 Khi cần khôi phục
Để khôi phục export cho tất cả user, chỉ cần xóa dòng:
```php
$conditions['user_ids'] = [2845];
```

## Lý do thay đổi
- Test chức năng export với dữ liệu cụ thể
- Giảm thời gian export khi test
- Tập trung vào user có dữ liệu để kiểm tra logic

## Lưu ý
⚠️ **Đây là thay đổi tạm thời** - Cần khôi phục khi hoàn thành test

## Cách test
1. Truy cập `/timesheets`
2. Click nút "Export"
3. File Excel sẽ chỉ chứa dữ liệu của user "Văn Đức Hải Trung" (ID: 2845)

## Khôi phục
Để khôi phục export cho tất cả user:

```php
public function export(ExportTimesheetRequest $exportTimesheetRequest)
{
    $this->authorize('export', Timesheet::class);

    return $this->timesheetService->export($exportTimesheetRequest->validated());
}
```

## Status
🟡 **TEMPORARY CHANGE** - Cần khôi phục sau khi test xong
