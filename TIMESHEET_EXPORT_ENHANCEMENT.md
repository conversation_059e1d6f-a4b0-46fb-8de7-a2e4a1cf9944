# Cải tiến Export Timesheets - Thêm Cột Giải Trình và Tính Phạt

## Tổng quan
Đã thêm các cột từ "Xác nhận tình trạng chấm công" đến "Tăng ca" vào file export timesheets, bao gồm logic tính toán từ giải trình công và bảng phạt theo cấp bậc.

## Các cột mới đã thêm

### 1. Cột L: Xác nhận tình trạng chấm công
- **Bình thường**: Không có vi phạm và không có giải trình
- **Có vi phạm**: C<PERSON> đi muộn/về sớm mà không có giải trình
- **Đã giải trình đi muộn**: <PERSON><PERSON> giải trình đi muộn được duyệt
- **<PERSON><PERSON> giải trình về sớm**: <PERSON><PERSON> giải trình về sớm được duyệt
- **Đã giải trình thiếu giờ**: Có giải trình thiếu giờ/quên checkin/checkout được duyệt
- **Có tăng ca**: Có giải trình tăng ca được duyệt
- **Làm việc từ xa**: Có giải trình làm việc từ xa được duyệt
- **Đã giải trình khác**: Có giải trình loại khác được duyệt

### 2. Cột M: Giải trình công
- Tóm tắt tất cả giải trình đã được duyệt cho ngày đó
- Format: "Loại giải trình: Nội dung; Loại khác: Nội dung khác"

### 3. Cột N: Công (sau điều chỉnh)
- Công sau khi áp dụng giải trình đã được duyệt
- Nếu có giải trình được duyệt (trừ OT) = 1.0 công
- Nếu không có giải trình = công tính từ máy

### 4. Cột O: Số phút đi muộn
- Tính từ giờ checkin thực tế so với giờ bắt đầu ca
- Chỉ hiển thị khi không có giải trình đi muộn được duyệt

### 5. Cột P: GT đi muộn
- Nội dung giải trình đi muộn (nếu có)

### 6. Cột Q: Phạt đi muộn
- Tính theo bảng phạt và cấp bậc
- Định dạng: #,##0 (VNĐ)

### 7. Cột R: Số phút về sớm
- Tính từ giờ checkout thực tế so với giờ kết thúc ca
- Chỉ hiển thị khi không có giải trình về sớm được duyệt

### 8. Cột S: GT về sớm
- Nội dung giải trình về sớm (nếu có)

### 9. Cột T: Phạt về sớm
- Tính theo bảng phạt và cấp bậc
- Định dạng: #,##0 (VNĐ)

### 10. Cột U: Số lần vi phạm
- Tổng số lần vi phạm (đi muộn + về sớm)
- Chỉ tính khi không có giải trình được duyệt

### 11. Cột V: Tổng tiền phạt
- Tổng phạt đi muộn + phạt về sớm
- Định dạng: #,##0 (VNĐ)

### 12. Cột W: Phạt công
- Hiện tại = 0 (có thể mở rộng sau)

### 13. Cột X: Tổng công
- Bằng với "Công (sau điều chỉnh)"

### 14. Cột Y: Tăng ca
- Tổng giờ tăng ca từ các giải trình OT được duyệt
- Định dạng: 0.0 (giờ)

## Bảng phạt đã áp dụng

### Nhân viên (user_department_chief_flag = false)
| Thời gian vi phạm | Mức phạt (VNĐ) |
|-------------------|----------------|
| ≤ 1 phút          | 0              |
| 1.01 - 10 phút    | 50,000         |
| 11.01 - 30 phút   | 100,000        |
| 31.01 - 60 phút   | 120,000        |
| 61.01 - 120 phút  | 200,000        |
| > 120 phút        | 0 (xử lý khác) |

### Trưởng nhóm (user_department_chief_flag = true)
| Thời gian vi phạm | Mức phạt (VNĐ) |
|-------------------|----------------|
| ≤ 1 phút          | 0              |
| 1.01 - 10 phút    | 50,000         |
| 11.01 - 30 phút   | 100,000        |
| 31.01 - 60 phút   | 150,000        |
| 61.01 - 120 phút  | 250,000        |
| > 120 phút        | 0 (xử lý khác) |

## Logic xử lý

### 1. Có giải trình được duyệt
- Lấy tất cả giải trình có `final_status = 'approved'` cho ngày đó
- Áp dụng theo loại giải trình:
  - **late, early, insufficient_hours, no_checkin, no_checkout, remote_work, other**: Điều chỉnh công = 1.0
  - **overtime**: Không ảnh hưởng công, chỉ cộng giờ OT
- Không tính vi phạm và phạt tiền

### 2. Không có giải trình
- Tính vi phạm dựa trên:
  - Đi muộn: `checkin > shift_start_time`
  - Về sớm: `checkout < shift_end_time`
- Tính phạt tiền theo bảng phạt và cấp bậc
- Công = công tính từ máy

## Files đã thay đổi

### 1. TimesheetRows.php
**File**: `app/Exports/Timesheet/TimesheetSheet/TimesheetRows.php`

**Thay đổi chính:**
- Thêm import `AttendanceExplanation`
- Cập nhật method `mapRow()` để lấy giải trình
- Cập nhật method `map()` để thêm 14 cột mới
- Thêm method `getAttendanceExplanations()`
- Thêm method `calculateAttendanceData()`
- Thêm method `applyExplanations()`
- Thêm method `calculateViolations()`
- Thêm method `calculatePenalty()`
- Cập nhật `columnFormats()` để định dạng cột mới

### 2. Headers đã có sẵn
**File**: `app/Exports/Timesheet/TimesheetSheet/TimesheetHeadings.php`
- Headers đã có đầy đủ các cột cần thiết
- Không cần thay đổi

### 3. Query đã có sẵn
**File**: `app/Repositories/Eloquent/TimesheetRepository.php`
- Query `queryToExport()` đã có `shift_start_time` và `shift_end_time`
- Không cần thay đổi

## Định dạng Excel

### Cột số
- **O, R, U**: Số nguyên (0)
- **N, W, X, Y**: Số thập phân (0.0)
- **Q, T, V**: Tiền tệ (#,##0)

### Màu sắc headers (đã có sẵn)
- **Cột N**: Màu đỏ (Công sau điều chỉnh)
- **Cột Q**: Màu đỏ (Phạt đi muộn)
- **Cột T**: Màu đỏ (Phạt về sớm)
- **Cột V**: Màu đỏ (Tổng tiền phạt)

## Test và Validation

### ✅ Đã test thành công:
- Export không có lỗi
- Logic tính toán hoạt động
- Định dạng cột đúng
- Headers hiển thị đầy đủ

### 🔄 Cần test thêm:
- Export với dữ liệu thực có giải trình
- Kiểm tra tính toán phạt tiền
- Kiểm tra logic OT
- Test với nhiều loại giải trình

## Lưu ý

### 1. Performance
- Query đã được optimize với GROUP BY
- Sử dụng MIN/MAX cho dữ liệu shift
- Giải trình được query riêng cho từng ngày

### 2. Dữ liệu
- Chỉ lấy giải trình có `final_status = 'approved'`
- Vi phạm chỉ tính khi không có giải trình tương ứng
- Phạt tiền > 120 phút = 0 (có thể có hình thức xử lý khác)

### 3. Mở rộng
- Có thể thêm logic phạt công (cột W)
- Có thể tùy chỉnh bảng phạt theo quy định mới
- Có thể thêm loại giải trình mới

## Kết luận

✅ **Đã hoàn thành:** Thêm 14 cột mới vào export timesheets với đầy đủ logic tính toán từ giải trình và bảng phạt theo cấp bậc.

🎯 **Kết quả:** File export giờ đây có thông tin chi tiết về tình trạng chấm công, giải trình, vi phạm, phạt tiền và tăng ca.
