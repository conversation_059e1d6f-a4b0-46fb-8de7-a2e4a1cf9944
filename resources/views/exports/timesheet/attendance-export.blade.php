@if(isset($isExport) && $isExport)
    <div style="margin-bottom: 20px; text-align: center;">
        <h2>BẢNG CHẤM CÔNG THÁNG {{ $month ?? date('n') }}/{{ $year ?? date('Y') }}</h2>
        @if($department)
            <p><strong>Phòng ban: {{ $department }}</strong></p>
        @endif
        <p><em>Xuất lúc: {{ now()->format('d/m/Y H:i:s') }}</em></p>
    </div>
@endif

<table class="table table-bordered">
    <thead>
        <tr style="background-color: #f8f9fa; font-weight: bold;">
            <th style="width: 80px;">Mã nhân viên</th>
            <th style="width: 150px;">Tên nhân viên</th>
            <th style="width: 120px;">Phòng ban</th>
            <th style="width: 100px;">Chứ<PERSON> vụ</th>
            <th style="width: 80px;"><PERSON><PERSON>y</th>
            <th style="width: 60px;">Thứ</th>
            <th style="width: 80px;">Giờ vào</th>
            <th style="width: 80px;">Giờ ra</th>
            <th style="width: 80px;">Công máy</th>
            <th style="width: 120px;">Ca làm việc</th>
            <th style="width: 150px;">Xác nhận tình trạng chấm công</th>
            <th style="width: 200px;">Giải trình công</th>
            <th style="width: 60px;">Công</th>
            <th style="width: 100px;">Số phút đi muộn</th>
            <th style="width: 100px;">GT đi muộn</th>
            <th style="width: 100px;">Phạt đi muộn</th>
            <th style="width: 100px;">Số phút về sớm</th>
            <th style="width: 100px;">GT về sớm</th>
            <th style="width: 100px;">Phạt về sớm</th>
            <th style="width: 100px;">Số lần vi phạm</th>
            <th style="width: 100px;">Tổng tiền phạt</th>
            <th style="width: 80px;">Phạt công</th>
            <th style="width: 80px;">Tổng công</th>
            <th style="width: 80px;">Tăng ca</th>
            <th style="width: 100px;">Ca làm việc (mã)</th>
            <th style="width: 100px;">Giờ vào (dự kiến)</th>
            <th style="width: 100px;">Giờ ra (dự kiến)</th>
            <th style="width: 120px;">Ca làm việc phụ</th>
            <th style="width: 100px;">Giờ vào phụ</th>
            <th style="width: 100px;">Giờ ra phụ</th>
        </tr>
    </thead>
    <tbody>
        @forelse($timesheets as $timesheet)
            <tr>
                <td style="text-align: center;">{{ $timesheet->user_code ?? 'N/A' }}</td>
                <td>{{ $timesheet->user_name ?? 'N/A' }}</td>
                <td>{{ $timesheet->department_name ?? 'N/A' }}</td>
                <td>{{ $timesheet->position ?? 'N/A' }}</td>
                <td style="text-align: center;">{{ $timesheet->date ? \Carbon\Carbon::parse($timesheet->date)->format('d/m/Y') : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->date ? \Carbon\Carbon::parse($timesheet->date)->locale('vi')->dayName : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->checkin_time ? \Carbon\Carbon::parse($timesheet->checkin_time)->format('H:i') : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->checkout_time ? \Carbon\Carbon::parse($timesheet->checkout_time)->format('H:i') : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->machine_workday ?? 0 }}</td>
                <td>{{ $timesheet->shift_name ?? 'N/A' }}</td>
                <td>{{ $timesheet->attendance_status ?? 'Chưa xác nhận' }}</td>
                <td style="word-wrap: break-word;">{{ $timesheet->explanation_summary ?? '' }}</td>
                <td style="text-align: center;">{{ $timesheet->final_workday ?? 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->late_minutes ?? 0 }}</td>
                <td>{{ $timesheet->late_explanation ?? '' }}</td>
                <td style="text-align: right;">{{ $timesheet->late_penalty ? number_format($timesheet->late_penalty, 0, ',', '.') : 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->early_minutes ?? 0 }}</td>
                <td>{{ $timesheet->early_explanation ?? '' }}</td>
                <td style="text-align: right;">{{ $timesheet->early_penalty ? number_format($timesheet->early_penalty, 0, ',', '.') : 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->violation_count ?? 0 }}</td>
                <td style="text-align: right;">{{ $timesheet->total_penalty ? number_format($timesheet->total_penalty, 0, ',', '.') : 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->penalty_workday ?? 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->total_workday ?? 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->overtime_hours ?? 0 }}</td>
                <td style="text-align: center;">{{ $timesheet->shift_code ?? 'N/A' }}</td>
                <td style="text-align: center;">{{ $timesheet->expected_checkin ? \Carbon\Carbon::parse($timesheet->expected_checkin)->format('H:i') : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->expected_checkout ? \Carbon\Carbon::parse($timesheet->expected_checkout)->format('H:i') : '' }}</td>
                <td>{{ $timesheet->secondary_shift_name ?? '' }}</td>
                <td style="text-align: center;">{{ $timesheet->secondary_checkin ? \Carbon\Carbon::parse($timesheet->secondary_checkin)->format('H:i') : '' }}</td>
                <td style="text-align: center;">{{ $timesheet->secondary_checkout ? \Carbon\Carbon::parse($timesheet->secondary_checkout)->format('H:i') : '' }}</td>
            </tr>
        @empty
            <tr>
                <td colspan="30" style="text-align: center; font-style: italic; color: #666;">
                    Không có dữ liệu chấm công cho điều kiện đã chọn.
                </td>
            </tr>
        @endforelse
    </tbody>
</table>

@if(isset($isExport) && $isExport && isset($timesheets) && $timesheets->count() > 0)
    <div style="margin-top: 30px;">
        <h3>THỐNG KÊ TỔNG HỢP</h3>
        @php
            $totalRecords = $timesheets->count();
            $totalWorkdays = $timesheets->sum('final_workday');
            $totalOvertimeHours = $timesheets->sum('overtime_hours');
            $totalPenalty = $timesheets->sum('total_penalty');
            $uniqueUsers = $timesheets->groupBy('user_id')->count();
        @endphp
        
        <table class="table table-bordered" style="width: 50%; margin-top: 15px;">
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <th>Chỉ số</th>
                <th style="text-align: center;">Giá trị</th>
            </tr>
            <tr>
                <td>Tổng số bản ghi chấm công</td>
                <td style="text-align: center; font-weight: bold;">{{ number_format($totalRecords) }}</td>
            </tr>
            <tr>
                <td>Số nhân viên</td>
                <td style="text-align: center;">{{ number_format($uniqueUsers) }}</td>
            </tr>
            <tr>
                <td>Tổng công</td>
                <td style="text-align: center; font-weight: bold;">{{ number_format($totalWorkdays, 1) }}</td>
            </tr>
            <tr>
                <td>Tổng giờ tăng ca</td>
                <td style="text-align: center;">{{ number_format($totalOvertimeHours, 1) }}</td>
            </tr>
            <tr>
                <td>Tổng tiền phạt</td>
                <td style="text-align: center; color: red; font-weight: bold;">{{ number_format($totalPenalty, 0, ',', '.') }} VNĐ</td>
            </tr>
        </table>
    </div>
@endif
