@extends('layouts.authenticated')

@section('page.title', __('common.list_page_title', ['obj' => __('objects.timesheet')]))

@section('page.css')
    <link rel="stylesheet" href="{{ asset('css/timesheet-custom.css') }}?v={{ time() }}">
@endsection

@push('scripts')
<script>
    $(function() {
        // Initialize Select2
        $('.select2').select2({
            width: '100%',
            allowClear: true,
            language: {
                noResults: function() {
                    return "Không tìm thấy kết quả";
                },
                searching: function() {
                    return "Đang tìm...";
                }
            }
        });

        // Toggle filter content
        $('#toggle-filter').on('click', function(e) {
            e.preventDefault();
            $('#filter-content').slideToggle('fast');
            $(this).find('i').toggleClass('fa-chevron-down fa-chevron-up');
        });

        // Check if any filter is applied
        var hasFilter = {{ count(request()->except(['page'])) > 0 ? 'true' : 'false' }};

        // Show/hide filter content based on whether filters are applied
        if (hasFilter) {
            $('#filter-content').show();
            $('#toggle-filter').find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
        } else {
            $('#filter-content').hide();
        }
    });
</script>
@endpush

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i>{{ __('common.home_breadcrumb') }}</a></li>
        <li class="active">{{ __('objects.timesheet') }}</li>
    </ol>
@endsection

@section('page.content')
    <div class="box">
        <div class="box-header with-border">
            <div class="object-list-action text-right">
                @can('create', \App\Models\Timesheet::class)
                    @include('includes.button.create', [
                        'url' => route('timesheets.create'),
                    ])
                @endcan

                @can('export', \App\Models\Timesheet::class)
                    <a id="timesheet-export" href="#" style="margin-left: 10px;"
                       title="Export Chi Tiết" class="btn btn-primary">Export Chi Tiết</a>
                    <a id="timesheet-export-summary" href="#" style="margin-left: 10px;"
                       title="Export Tổng Hợp" class="btn btn-success">Export Tổng Hợp</a>
                @endcan
            </div>
        </div>
        <div class="box-body">
            <div class="object-list-filter">
                <form method="GET" action="{{ route('timesheets.index') }}" id="filter-form">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="filter-section">
                                <h4 style="color: #3c8dbc;"><i class="fa fa-filter" style="color: #3c8dbc;"></i> {{ __('common.filter') }}</h4>
                                <button type="button" class="btn btn-xs btn-default pull-right" id="toggle-filter">
                                    <i class="fa fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="filter-content" id="filter-content">
                        <div class="row">
                            <!-- Employee Code -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Mã nhân viên</label>
                                    <select name="codes[]" class="form-control select2" multiple data-placeholder="{{ __('common.all') }}">
                                        @foreach($filters['data']['users'] as $user)
                                            <option value="{{ $user->id }}" {{ in_array($user->id, $filters['params']['codes'] ?? []) ? 'selected' : '' }}>
                                                {{ $user->code }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Employee Name -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Tên nhân viên</label>
                                    <select name="user_ids[]" class="form-control select2" multiple data-placeholder="{{ __('common.all') }}">
                                        @foreach($filters['data']['users'] as $user)
                                            <option value="{{ $user->id }}" {{ in_array($user->id, $filters['params']['user_ids'] ?? []) ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Department -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Phòng ban</label>
                                    <select name="staff_department_ids[]" class="form-control select2" multiple data-placeholder="{{ __('common.all') }}">
                                        @foreach($filters['data']['staffDepartments'] as $department)
                                            <option value="{{ $department->id }}" {{ in_array($department->id, $filters['params']['staff_department_ids'] ?? []) ? 'selected' : '' }}>
                                                {{ $department->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Address -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Địa chỉ</label>
                                    <select name="company_address_ids[]" class="form-control select2" multiple data-placeholder="{{ __('common.all') }}">
                                        @foreach($filters['data']['companyAddresses'] as $address)
                                            <option value="{{ $address->id }}" {{ in_array($address->id, $filters['params']['company_address_ids'] ?? []) ? 'selected' : '' }}>
                                                {{ $address->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Date range -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Khoảng thời gian</label>
                                    <div class="row">
                                        <div class="col-xs-6">
                                            <input type="date" class="form-control" name="from_date" value="{{ $filters['params']['from_date'] ?? '' }}"
                                                   placeholder="Từ ngày">
                                        </div>
                                        <div class="col-xs-6">
                                            <input type="date" class="form-control" name="to_date" value="{{ $filters['params']['to_date'] ?? '' }}"
                                                   placeholder="Đến ngày">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 filter-buttons">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> {{ __('common.search') }}
                                </button>
                                <a href="{{ route('timesheets.index') }}" class="btn btn-default">
                                    <i class="fa fa-refresh"></i> {{ __('common.reset') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-responsive">
                @if($timesheets->isNotEmpty())
                    <table class="table table-bordered table-hover table-custom">
                        <thead>
                            <tr>
                                <th style="width: 50px;">STT</th>
                                <th style="width: 200px;">Nhân viên</th>
                                <th style="width: 120px;">Ngày</th>
                                <th style="width: 100px;">Giờ vào</th>
                                <th style="width: 100px;">Giờ ra</th>
                                <th style="width: 250px;">Thời gian làm việc</th>
                                <th style="width: 100px;">Công</th>
                                <th style="width: 200px;">Địa điểm</th>
                                <th style="width: 130px; text-align: center;">Hành động</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($timesheets as $index => $timesheet)
                                <tr>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td>
                                        <div class="timesheet-info">
                                            <span class="employee-name">{{ $timesheet?->user->name }}</span>
                                            <span class="employee-code">{{ $timesheet?->user->code }}</span>
                                            <span class="employee-department">{{ $timesheet?->user?->staffDepartment?->name }}</span>
                                            <span class="timesheet-shift">{{ $timesheet->shift?->name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="timesheet-date">
                                            <div>{{ $timesheet->date?->format(config('common.datetime.format.client.date')) }}</div>
                                            <div>{{ get_day_of_weeks($timesheet->date) }}</div>
                                        </div>
                                    </td>
                                    <td>{{ $timesheet->checkin?->format(config('common.datetime.format.client.time')) }}</td>
                                    <td>{{ $timesheet->checkout?->format(config('common.datetime.format.client.time')) }}</td>
                                    <td>
                                        <div class="hours-display">
                                            <div class="hours-item">
                                                <span class="hours-label" title="Tổng thời gian từ checkin đến checkout, không trừ thời gian nghỉ">Số giờ làm thực tế (Theo checkin-out):</span>
                                                <span class="hours-value">{{ $timesheet->calcWorkDayRealHours() }}</span>
                                            </div>
                                            <div class="hours-item">
                                                <span class="hours-label" title="Thời gian làm việc được tính công, giới hạn trong khung giờ làm việc của ca và trừ thời gian nghỉ">Số giờ làm thực tế (Để tính công):</span>
                                                <span class="hours-value">{{ $timesheet->calcWorkDayHours() }}</span>
                                            </div>
                                            <div class="hours-item">
                                                <span class="hours-label">Số giờ tối thiểu để đạt 1 công:</span>
                                                <span class="hours-value">{{ $timesheet->shift?->workday_min_1 }}</span>
                                            </div>
                                            <div class="hours-item">
                                                <span class="hours-label">Số giờ tối thiểu để đạt 0.5 công:</span>
                                                <span class="hours-value">{{ $timesheet->shift?->workday_min_2 }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="workday-value">{{ $timesheet->calcWorkday() }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <small><strong>Check-in:</strong> {{ $timesheet->companyAddress?->name }}</small>
                                        </div>
                                        <div>
                                            <small><strong>Check-out:</strong> {{ $timesheet->checkoutCompanyAddress?->name }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            @can('update', $timesheet)
                                                @include('includes.button.edit', [
                                                    'url' => route('timesheets.edit', ['timesheet' => $timesheet->getKey(), 'redirect_url' => $redirectUrl])
                                                ])
                                            @endcan
                                            @can('delete', $timesheet)
                                                <a href='javascript:void(0);'
                                                   data-url="{{ route('timesheets.destroy', $timesheet->getKey()) }}"
                                                   data-message="{{ __('messages.are_you_sure_to_delete_object', ['obj' => __('objects.timesheet'), 'id' => $timesheet->id]) }}"
                                                   data-form-id="js_form_delete_object"
                                                   class="btn btn-sm btn-danger timesheet-delete-btn"
                                                >
                                                    {{ __('common.delete_action') }}
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    @include('includes.common.table-no-data')
                @endif
            </div>

            <div class="text-right">
                {{ $timesheets->appends(request()->input())->links() }}
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div aria-hidden="false" role="dialog" aria-labelledby="deleteModalLabel"
         class="modal fade" id="deleteModal" style="display: none;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 id="deleteModalLabel" class="modal-title">
                        <i class="fa fa-trash-o text-danger"></i> Xác nhận xóa
                    </h4>
                </div>
                <div id='delete_modal_content' class="modal-body">
                    <div class="text-center" style="margin-bottom: 15px;">
                        <i class="fa fa-exclamation-triangle text-warning" style="font-size: 48px;"></i>
                    </div>
                    <p id="delete-message" class="text-center"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> Hủy
                    </button>
                    <button type="button" class="btn btn-danger" id="confirm-delete">
                        <i class="fa fa-trash-o"></i> Xóa
                    </button>
                </div>
            </div>
        </div>
    </div>

    @include('includes.form.delete')
@endsection

@push('scripts')
<script>
    // Delete confirmation with modal
    $(function() {
        // Store the delete URL and form ID
        let deleteUrl = '';
        let deleteFormId = '';

        // Use our custom class for delete buttons
        $(document).on('click', '.timesheet-delete-btn', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Stop event propagation

            // Get data attributes
            deleteUrl = $(this).data('url');
            deleteFormId = $(this).data('form-id');
            const message = $(this).data('message');

            // Set message in modal
            $('#delete-message').text(message);

            // Add animation class to warning icon
            $('.fa-exclamation-triangle').addClass('animated pulse');

            // Show modal with fade effect
            $('#deleteModal').modal({
                backdrop: 'static',
                keyboard: false,
                show: true
            });

            // Prevent default browser confirm dialog
            return false;
        });

        // When confirm delete button is clicked
        $('#confirm-delete').on('click', function() {
            const form = $('form#' + deleteFormId);

            // Add loading state to button
            const $btn = $(this);
            $btn.html('<i class="fa fa-spinner fa-spin"></i> Đang xóa...');
            $btn.prop('disabled', true);

            if (deleteUrl && form.length) {
                // Short delay for better UX
                setTimeout(function() {
                    form.attr('action', deleteUrl);
                    form.submit();

                    // Hide modal
                    $('#deleteModal').modal('hide');
                }, 500);
            } else {
                // If something went wrong, restore button
                $btn.html('<i class="fa fa-trash-o"></i> Xóa');
                $btn.prop('disabled', false);
                $('#deleteModal').modal('hide');
            }
        });

        // Reset button state when modal is hidden
        $('#deleteModal').on('hidden.bs.modal', function() {
            $('#confirm-delete').html('<i class="fa fa-trash-o"></i> Xóa');
            $('#confirm-delete').prop('disabled', false);
            $('.fa-exclamation-triangle').removeClass('animated pulse');
        });
    });

    // Export functionality
    var EXPORT_TIMESHEET_URL = "{{ route('timesheets.export') }}";
    var EXPORT_SUMMARY_URL = "{{ route('timesheets.export-summary') }}";

    $(document).on('click', '#timesheet-export', function(e) {
        e.preventDefault();

        // Get all form data
        var formData = $('#filter-form').serialize();

        // Redirect to export URL with all filters
        window.location.href = EXPORT_TIMESHEET_URL + '?' + formData;
    });

    $(document).on('click', '#timesheet-export-summary', function(e) {
        e.preventDefault();

        // Get all form data
        var formData = $('#filter-form').serialize();

        // Redirect to export summary URL with all filters
        window.location.href = EXPORT_SUMMARY_URL + '?' + formData;
    });
</script>
@endpush
