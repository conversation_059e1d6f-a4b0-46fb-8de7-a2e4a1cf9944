@extends('layouts.authenticated')

@section('page.title', 'Xuất bảng chấm công')

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li class="active">Xuất bảng chấm công</li>
    </ol>
@endsection

@section('page.content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-file-excel-o"></i> Xuất bảng chấm công tháng
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong><PERSON><PERSON><PERSON> ý:</strong> <PERSON><PERSON><PERSON> chấm công sẽ bao gồm dữ liệu đã được điều chỉnh theo các giải trình đã được duyệt.
                    </div>

                    <form id="exportForm" method="GET" action="{{ route('timesheet.attendance.export-excel') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="month">Tháng <span class="text-danger">*</span></label>
                                    <select name="month" id="month" class="form-control" required>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                Tháng {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="year">Năm <span class="text-danger">*</span></label>
                                    <select name="year" id="year" class="form-control" required>
                                        @for($year = date('Y'); $year >= date('Y') - 3; $year--)
                                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="department_id">Phòng ban (tùy chọn)</label>
                                    <select name="department_id" id="department_id" class="form-control">
                                        <option value="">-- Tất cả phòng ban --</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}">{{ $department->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" id="previewBtn" class="btn btn-info">
                                            <i class="fa fa-eye"></i> Xem trước
                                        </button>
                                        <button type="submit" id="exportBtn" class="btn btn-success">
                                            <i class="fa fa-download"></i> Xuất Excel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chọn nhân viên cụ thể -->
                        <div class="row" id="userSelectionRow" style="display: none;">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="user_ids">Chọn nhân viên cụ thể (tùy chọn)</label>
                                    <select name="user_ids[]" id="user_ids" class="form-control" multiple>
                                        <!-- Options sẽ được load bằng AJAX -->
                                    </select>
                                    <small class="text-muted">Để trống để xuất tất cả nhân viên trong phòng ban</small>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Preview Section -->
                    <div id="previewSection" style="display: none; margin-top: 30px;">
                        <h4><i class="fa fa-eye"></i> Xem trước dữ liệu</h4>
                        <div id="previewStats" class="row" style="margin-bottom: 20px;">
                            <!-- Stats sẽ được load bằng AJAX -->
                        </div>
                        <div id="previewTable" class="table-responsive">
                            <!-- Table sẽ được load bằng AJAX -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page.scripts')
<script>
$(document).ready(function() {
    // Initialize Select2 for user selection
    $('#user_ids').select2({
        placeholder: 'Chọn nhân viên...',
        allowClear: true,
        width: '100%'
    });

    // Handle department change
    $('#department_id').on('change', function() {
        var departmentId = $(this).val();
        
        if (departmentId) {
            $('#userSelectionRow').show();
            loadUsersByDepartment(departmentId);
        } else {
            $('#userSelectionRow').hide();
            $('#user_ids').empty().trigger('change');
        }
    });

    // Load users by department
    function loadUsersByDepartment(departmentId) {
        $.ajax({
            url: '{{ route("timesheet.attendance.users-by-department") }}',
            method: 'GET',
            data: { department_id: departmentId },
            beforeSend: function() {
                $('#user_ids').empty().append('<option value="">Đang tải...</option>');
            },
            success: function(response) {
                $('#user_ids').empty();
                if (response.success && response.data.length > 0) {
                    $.each(response.data, function(index, user) {
                        $('#user_ids').append(
                            '<option value="' + user.id + '">' + 
                            user.user_code + ' - ' + user.name + 
                            '</option>'
                        );
                    });
                } else {
                    $('#user_ids').append('<option value="">Không có nhân viên nào</option>');
                }
                $('#user_ids').trigger('change');
            },
            error: function() {
                $('#user_ids').empty().append('<option value="">Lỗi khi tải dữ liệu</option>');
            }
        });
    }

    // Handle preview button
    $('#previewBtn').on('click', function() {
        var formData = $('#exportForm').serialize();
        
        $.ajax({
            url: '{{ route("timesheet.attendance.preview") }}',
            method: 'GET',
            data: formData,
            beforeSend: function() {
                $('#previewBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Đang tải...');
            },
            success: function(response) {
                if (response.success) {
                    displayPreview(response.data, response.stats);
                    $('#previewSection').show();
                } else {
                    alert('Lỗi: ' + response.message);
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra khi tải dữ liệu';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                alert(message);
            },
            complete: function() {
                $('#previewBtn').prop('disabled', false).html('<i class="fa fa-eye"></i> Xem trước');
            }
        });
    });

    // Display preview data
    function displayPreview(data, stats) {
        // Display stats
        var statsHtml = `
            <div class="col-md-2">
                <div class="info-box bg-blue">
                    <span class="info-box-icon"><i class="fa fa-list"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Tổng bản ghi</span>
                        <span class="info-box-number">${stats.total_records}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box bg-green">
                    <span class="info-box-icon"><i class="fa fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Nhân viên</span>
                        <span class="info-box-number">${stats.unique_users}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box bg-yellow">
                    <span class="info-box-icon"><i class="fa fa-calendar"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Tổng công</span>
                        <span class="info-box-number">${stats.total_workdays}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box bg-purple">
                    <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Tăng ca (giờ)</span>
                        <span class="info-box-number">${stats.total_overtime}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box bg-red">
                    <span class="info-box-icon"><i class="fa fa-exclamation-triangle"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Vi phạm</span>
                        <span class="info-box-number">${stats.total_violations}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box bg-gray">
                    <span class="info-box-icon"><i class="fa fa-eye"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Xem trước</span>
                        <span class="info-box-number">${stats.preview_records}</span>
                    </div>
                </div>
            </div>
        `;
        $('#previewStats').html(statsHtml);

        // Display table (simplified version)
        var tableHtml = `
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>Mã NV</th>
                        <th>Tên nhân viên</th>
                        <th>Ngày</th>
                        <th>Giờ vào</th>
                        <th>Giờ ra</th>
                        <th>Công</th>
                        <th>Tăng ca</th>
                        <th>Trạng thái</th>
                    </tr>
                </thead>
                <tbody>
        `;

        if (data.length > 0) {
            $.each(data, function(index, record) {
                var checkinTime = record.checkin_time ? new Date(record.checkin_time).toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'}) : '';
                var checkoutTime = record.checkout_time ? new Date(record.checkout_time).toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'}) : '';
                var date = new Date(record.date).toLocaleDateString('vi-VN');
                
                tableHtml += `
                    <tr>
                        <td>${record.user_code}</td>
                        <td>${record.user_name}</td>
                        <td>${date}</td>
                        <td>${checkinTime}</td>
                        <td>${checkoutTime}</td>
                        <td>${record.final_workday}</td>
                        <td>${record.overtime_hours}</td>
                        <td>${record.attendance_status}</td>
                    </tr>
                `;
            });
        } else {
            tableHtml += '<tr><td colspan="8" class="text-center">Không có dữ liệu</td></tr>';
        }

        tableHtml += '</tbody></table>';
        $('#previewTable').html(tableHtml);
    }

    // Handle export form submission
    $('#exportForm').on('submit', function(e) {
        var $btn = $('#exportBtn');
        $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Đang xuất...');
        
        // Reset button after 3 seconds
        setTimeout(function() {
            $btn.prop('disabled', false).html('<i class="fa fa-download"></i> Xuất Excel');
        }, 3000);
    });
});
</script>
@endsection
