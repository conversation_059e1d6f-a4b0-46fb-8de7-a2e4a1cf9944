@extends('layouts.authenticated')

@section('page.title', $isEdit ? 'Sửa đánh giá nhân viên' : 'Thêm đánh giá nhân viên')

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li><a href="{{ route('user-ratings.index') }}">Đánh giá xếp hạng nhân viên</a></li>
        <li class="active">{{ $isEdit ? 'Sửa' : 'Thêm' }}</li>
    </ol>
@endsection

@section('page.content')
<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ $isEdit ? 'Sửa đánh giá nhân viên' : 'Thêm đánh giá nhân viên' }}</h3>
    </div>
    <form method="POST" action="{{ $isEdit ? route('user-ratings.update', $userRating) : route('user-ratings.store') }}">
        @csrf
        @if($isEdit)
            @method('PUT')
        @endif
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="user_id">Nhân viên <span class="text-danger">*</span></label>
                        <select name="user_id" id="user_id" class="form-control select2 @error('user_id') has-error @enderror" required>
                            <option value="">-- Chọn nhân viên --</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}"
                                    {{ old('user_id', $isEdit ? $userRating->user_id : '') == $user->id ? 'selected' : '' }}>
                                    {{ $user->name }} ({{ $user->code }})
                                </option>
                            @endforeach
                        </select>
                        @error('user_id')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="rating_date">Ngày đánh giá <span class="text-danger">*</span></label>
                        <input type="date" name="rating_date" id="rating_date"
                               class="form-control @error('rating_date') has-error @enderror"
                               value="{{ old('rating_date', $isEdit ? $userRating->rating_date->format('Y-m-d') : date('Y-m-d')) }}" required>
                        @error('rating_date')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="shop_id">Chi nhánh</label>
                        <select name="shop_id" id="shop_id" class="form-control select2 @error('shop_id') has-error @enderror">
                            <option value="">-- Chọn chi nhánh --</option>
                            @foreach($shops as $shop)
                                <option value="{{ $shop->id }}" 
                                    {{ old('shop_id', $isEdit ? $userRating->shop_id : '') == $shop->id ? 'selected' : '' }}>
                                    {{ $shop->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('shop_id')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="staff_department_id">Phòng ban</label>
                        <select name="staff_department_id" id="staff_department_id" class="form-control select2 @error('staff_department_id') has-error @enderror">
                            <option value="">-- Chọn phòng ban --</option>
                            @foreach($staffDepartments as $dept)
                                <option value="{{ $dept->id }}" 
                                    {{ old('staff_department_id', $isEdit ? $userRating->staff_department_id : '') == $dept->id ? 'selected' : '' }}>
                                    {{ $dept->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('staff_department_id')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="team_id">Team</label>
                        <select name="team_id" id="team_id" class="form-control select2 @error('team_id') has-error @enderror">
                            <option value="">-- Chọn team --</option>
                            @foreach($teams as $team)
                                <option value="{{ $team->id }}" 
                                    {{ old('team_id', $isEdit ? $userRating->team_id : '') == $team->id ? 'selected' : '' }}>
                                    {{ $team->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('team_id')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="team_type">Loại team</label>
                        <select name="team_type" id="team_type" class="form-control select2 @error('team_type') has-error @enderror">
                            <option value="">-- Chọn loại team --</option>
                            @foreach($teamTypeOptions as $value => $label)
                                <option value="{{ $value }}" 
                                    {{ old('team_type', $isEdit ? $userRating->team_type : '') == $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('team_type')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="appointment_score">Điểm lịch hẹn</label>
                        <input type="number" name="appointment_score" id="appointment_score" 
                               class="form-control @error('appointment_score') has-error @enderror"
                               min="0" max="10" step="0.1"
                               value="{{ old('appointment_score', $isEdit ? $userRating->appointment_score : '') }}">
                        @error('appointment_score')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="phone_score">Điểm số điện thoại</label>
                        <input type="number" name="phone_score" id="phone_score" 
                               class="form-control @error('phone_score') has-error @enderror"
                               min="0" max="10" step="0.1"
                               value="{{ old('phone_score', $isEdit ? $userRating->phone_score : '') }}">
                        @error('phone_score')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="service_quality_score">Điểm chất lượng dịch vụ</label>
                        <input type="number" name="service_quality_score" id="service_quality_score" 
                               class="form-control @error('service_quality_score') has-error @enderror"
                               min="0" max="10" step="0.1"
                               value="{{ old('service_quality_score', $isEdit ? $userRating->service_quality_score : '') }}">
                        @error('service_quality_score')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="invoice_score">Điểm đầu hóa đơn</label>
                        <input type="number" name="invoice_score" id="invoice_score" 
                               class="form-control @error('invoice_score') has-error @enderror"
                               min="0" max="10" step="0.1"
                               value="{{ old('invoice_score', $isEdit ? $userRating->invoice_score : '') }}">
                        @error('invoice_score')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <label for="notes">Ghi chú</label>
                        <textarea name="notes" id="notes" rows="3" 
                                  class="form-control @error('notes') has-error @enderror"
                                  placeholder="Nhập ghi chú (tùy chọn)">{{ old('notes', $isEdit ? $userRating->notes : '') }}</textarea>
                        @error('notes')
                            <span class="help-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        <div class="box-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-save"></i> {{ $isEdit ? 'Cập nhật' : 'Lưu' }}
            </button>
            <a href="{{ route('user-ratings.index') }}" class="btn btn-default">
                <i class="fa fa-times"></i> Hủy
            </a>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        allowClear: true
    });
    $('#user_id').on('change', function() {
        const userId = $(this).val();
        if (userId) {
            $.get(`/user-ratings/get-user-info/${userId}`)
                .done(function(data) {
                    if (data.shop_id) $('#shop_id').val(data.shop_id).trigger('change');
                    if (data.staff_department_id) $('#staff_department_id').val(data.staff_department_id).trigger('change');
                    if (data.team_id) $('#team_id').val(data.team_id).trigger('change');
                    if (data.team_type) $('#team_type').val(data.team_type).trigger('change');
                })
                .fail(function() {
                    console.log('Failed to fetch user info');
                });
        }
    });
});
</script>
@endpush
